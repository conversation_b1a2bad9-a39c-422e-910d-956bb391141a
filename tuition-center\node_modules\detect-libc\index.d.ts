// Copyright 2017 <PERSON><PERSON> and others.
// SPDX-License-Identifier: Apache-2.0

export const GLIBC: 'glibc';
export const MUSL: 'musl';

export function family(): Promise<string | null>;
export function familySync(): string | null;

export function isNonGlibcLinux(): Promise<boolean>;
export function isNonGlibcLinuxSync(): boolean;

export function version(): Promise<string | null>;
export function versionSync(): string | null;
