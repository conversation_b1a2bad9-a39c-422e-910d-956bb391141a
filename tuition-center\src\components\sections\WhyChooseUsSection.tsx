"use client";

import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Award,
  Clock,
  BookOpen,
  Target,
  TrendingUp,
  Star,
  Brain,
  Shield,
  Heart
} from "lucide-react";

const features = [
  {
    icon: Users,
    title: "Expert Faculty",
    description: "Our team consists of highly qualified teachers with 10+ years of experience in their respective subjects.",
    stats: "50+ Expert Teachers",
    color: "blue"
  },
  {
    icon: Target,
    title: "Small Batch Sizes",
    description: "We maintain small class sizes (15-20 students) to ensure personalized attention and better learning outcomes.",
    stats: "Max 20 Students/Batch",
    color: "green"
  },
  {
    icon: Clock,
    title: "Regular Assessments",
    description: "Weekly tests, monthly evaluations, and mock exams to track progress and identify areas for improvement.",
    stats: "Weekly Tests",
    color: "purple"
  },
  {
    icon: TrendingUp,
    title: "Proven Results",
    description: "95% of our students achieve their target scores with many securing top ranks in their respective examinations.",
    stats: "95% Success Rate",
    color: "orange"
  },
  {
    icon: BookOpen,
    title: "Comprehensive Study Material",
    description: "Well-researched notes, practice papers, and reference materials designed by our expert faculty.",
    stats: "Complete Study Package",
    color: "red"
  },
  {
    icon: Brain,
    title: "Modern Teaching Methods",
    description: "Interactive learning, visual aids, and technology-enhanced teaching for better concept understanding.",
    stats: "Interactive Learning",
    color: "indigo"
  },
  {
    icon: Shield,
    title: "Safe Learning Environment",
    description: "Clean, well-ventilated classrooms with proper safety measures and a conducive learning atmosphere.",
    stats: "Safe & Secure",
    color: "teal"
  },
  {
    icon: Heart,
    title: "Individual Care",
    description: "Personal mentoring, career guidance, and emotional support to help students overcome challenges.",
    stats: "Personal Mentoring",
    color: "pink"
  }
];

const achievements = [
  {
    number: "2000+",
    label: "Students Graduated",
    icon: Users
  },
  {
    number: "95%",
    label: "Success Rate",
    icon: Award
  },
  {
    number: "15+",
    label: "Years Experience",
    icon: Clock
  },
  {
    number: "50+",
    label: "Expert Teachers",
    icon: BookOpen
  }
];

const getColorClasses = (color: string) => {
  const colors = {
    blue: "bg-blue-100 text-blue-600",
    green: "bg-green-100 text-green-600",
    purple: "bg-purple-100 text-purple-600",
    orange: "bg-orange-100 text-orange-600",
    red: "bg-red-100 text-red-600",
    indigo: "bg-indigo-100 text-indigo-600",
    teal: "bg-teal-100 text-teal-600",
    pink: "bg-pink-100 text-pink-600"
  };
  return colors[color as keyof typeof colors] || colors.blue;
};

export default function WhyChooseUsSection() {
  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <Image
          src="https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=1920&h=1080&fit=crop"
          alt="Students collaborating"
          width={1920}
          height={1080}
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/95 to-gray-50/95"></div>
      </div>

      <div className="relative z-10">
        <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4 text-blue-600 bg-blue-100">
              Why Choose Us
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              What Makes Us Different
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              We don&apos;t just teach subjects; we build futures. Our unique approach combines academic excellence
              with personal development to ensure holistic growth of every student.
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            {features.map((feature, index) => {
              const IconComponent = feature.icon;
              return (
                <Card key={index} className="hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border-0 bg-white/80 backdrop-blur-sm">
                  <CardContent className="p-6 text-center">
                    <div className={`w-16 h-16 rounded-full ${getColorClasses(feature.color)} flex items-center justify-center mx-auto mb-4`}>
                      <IconComponent className="w-8 h-8" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-gray-600 text-sm mb-3 leading-relaxed">
                      {feature.description}
                    </p>
                    <Badge variant="outline" className="text-xs font-medium">
                      {feature.stats}
                    </Badge>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Achievements Section */}
          <div className="bg-white rounded-2xl p-8 shadow-lg mb-16">
            <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
              Our Achievements Speak for Themselves
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              {achievements.map((achievement, index) => {
                const IconComponent = achievement.icon;
                return (
                  <div key={index} className="text-center">
                    <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <IconComponent className="w-8 h-8 text-blue-600" />
                    </div>
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {achievement.number}
                    </div>
                    <div className="text-gray-600 text-sm">
                      {achievement.label}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Testimonial Highlight */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 text-white text-center">
            <div className="flex justify-center mb-4">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />
              ))}
            </div>
            <blockquote className="text-xl md:text-2xl font-medium mb-4 italic">
              "              &ldquo;Excellence Academy transformed my academic journey. The personalized attention and expert guidance
              helped me secure admission to my dream engineering college.&rdquo;"
            </blockquote>
            <div className="text-blue-200">
              <div className="font-semibold">Rajesh Sharma</div>
              <div className="text-sm">IOE Pulchowk Graduate, 2023</div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              Ready to Start Your Success Journey?
            </h3>
            <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
              Join thousands of successful students who have achieved their academic goals with our guidance. 
              Your success story starts here.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button 
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors"
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  contactSection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Get Enrolled Today
              </button>
              <button 
                className="border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold px-8 py-3 rounded-lg transition-colors"
                onClick={() => {
                  const aboutSection = document.getElementById('about');
                  aboutSection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Learn More About Us
              </button>
            </div>
          </div>
          </div>
        </div>
      </div>
    </section>
  );
}
