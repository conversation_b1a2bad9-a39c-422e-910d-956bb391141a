import type { TSESTree } from '@typescript-eslint/types';
import type { ScopeManager } from '../ScopeManager';
import type { Scope } from './Scope';
import { ScopeBase } from './ScopeBase';
import { ScopeType } from './ScopeType';
export declare class ClassStaticBlockScope extends ScopeBase<ScopeType.classStaticBlock, TSESTree.StaticBlock, Scope> {
    constructor(scopeManager: ScopeManager, upperScope: ClassStaticBlockScope['upper'], block: ClassStaticBlockScope['block']);
}
//# sourceMappingURL=ClassStaticBlockScope.d.ts.map