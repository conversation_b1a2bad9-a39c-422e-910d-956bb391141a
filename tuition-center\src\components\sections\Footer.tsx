"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  GraduationCap,
  Phone, 
  Mail, 
  MapPin, 
  Clock,
  Facebook,
  Instagram,
  Youtube,
  Twitter,
  ExternalLink,
  Heart
} from "lucide-react";

const quickLinks = [
  { name: "About Us", href: "#about" },
  { name: "Courses", href: "#courses" },
  { name: "Our Faculty", href: "#faculty" },
  { name: "Why Choose Us", href: "#why-choose-us" },
  { name: "Testimonials", href: "#testimonials" },
  { name: "Contact", href: "#contact" }
];

const courses = [
  { name: "SEE Preparation", href: "#contact" },
  { name: "+2 Science", href: "#contact" },
  { name: "+2 Management", href: "#contact" },
  { name: "Engineering Entrance", href: "#contact" },
  { name: "Medical Entrance", href: "#contact" },
  { name: "Foundation Course", href: "#contact" }
];

const socialLinks = [
  { 
    name: "Facebook", 
    icon: Facebook, 
    href: "https://facebook.com/excellenceacademy",
    color: "hover:text-blue-600"
  },
  { 
    name: "Instagram", 
    icon: Instagram, 
    href: "https://instagram.com/excellenceacademy",
    color: "hover:text-pink-600"
  },
  { 
    name: "YouTube", 
    icon: Youtube, 
    href: "https://youtube.com/excellenceacademy",
    color: "hover:text-red-600"
  },
  { 
    name: "Twitter", 
    icon: Twitter, 
    href: "https://twitter.com/excellenceacademy",
    color: "hover:text-blue-400"
  }
];

const contactInfo = [
  {
    icon: Phone,
    title: "Phone",
    details: ["+977-21-123456", "+977-**********"]
  },
  {
    icon: Mail,
    title: "Email",
    details: ["<EMAIL>", "<EMAIL>"]
  },
  {
    icon: MapPin,
    title: "Address",
    details: ["Main Road, Biratnagar-10", "Morang, Province 1, Nepal"]
  },
  {
    icon: Clock,
    title: "Hours",
    details: ["Mon-Fri: 6:00 AM - 8:00 PM", "Sat-Sun: 6:00 AM - 6:00 PM"]
  }
];

export default function Footer() {
  const scrollToSection = (href: string) => {
    if (href.startsWith('#')) {
      const element = document.getElementById(href.substring(1));
      element?.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {/* Company Info */}
            <div className="lg:col-span-1">
              <div className="flex items-center mb-6">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                  <GraduationCap className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">Excellence Academy</h3>
                  <p className="text-sm text-gray-400">Biratnagar</p>
                </div>
              </div>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Nurturing academic excellence since 2009. We are committed to providing 
                quality education and helping students achieve their dreams through 
                expert guidance and proven methodologies.
              </p>
              
              {/* Social Links */}
              <div className="flex space-x-4">
                {socialLinks.map((social, index) => {
                  const IconComponent = social.icon;
                  return (
                    <a
                      key={index}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center transition-colors ${social.color} hover:bg-gray-700`}
                      aria-label={social.name}
                    >
                      <IconComponent className="w-5 h-5" />
                    </a>
                  );
                })}
              </div>
            </div>

            {/* Quick Links */}
            <div>
              <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
              <ul className="space-y-3">
                {quickLinks.map((link, index) => (
                  <li key={index}>
                    <button
                      onClick={() => scrollToSection(link.href)}
                      className="text-gray-300 hover:text-white transition-colors text-left"
                    >
                      {link.name}
                    </button>
                  </li>
                ))}
              </ul>
            </div>

            {/* Courses */}
            <div>
              <h4 className="text-lg font-semibold mb-6">Our Courses</h4>
              <ul className="space-y-3">
                {courses.map((course, index) => (
                  <li key={index}>
                    <button
                      onClick={() => scrollToSection(course.href)}
                      className="text-gray-300 hover:text-white transition-colors text-left"
                    >
                      {course.name}
                    </button>
                  </li>
                ))}
              </ul>
            </div>

            {/* Contact Info */}
            <div>
              <h4 className="text-lg font-semibold mb-6">Contact Info</h4>
              <div className="space-y-4">
                {contactInfo.map((info, index) => {
                  const IconComponent = info.icon;
                  return (
                    <div key={index} className="flex items-start space-x-3">
                      <div className="w-5 h-5 text-blue-400 mt-1 flex-shrink-0">
                        <IconComponent className="w-5 h-5" />
                      </div>
                      <div>
                        <h5 className="font-medium text-white mb-1">{info.title}</h5>
                        {info.details.map((detail, detailIndex) => (
                          <p key={detailIndex} className="text-gray-300 text-sm">
                            {detail}
                          </p>
                        ))}
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>

          {/* Newsletter Signup */}
          <div className="mt-12 pt-8 border-t border-gray-800">
            <div className="text-center max-w-2xl mx-auto">
              <h4 className="text-xl font-semibold mb-4">Stay Updated</h4>
              <p className="text-gray-300 mb-6">
                Get the latest updates about admissions, courses, and academic achievements.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-white placeholder-gray-400"
                />
                <Button className="bg-blue-600 hover:bg-blue-700">
                  Subscribe
                </Button>
              </div>
            </div>
          </div>

          {/* Achievement Highlights */}
          <div className="mt-12 pt-8 border-t border-gray-800">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-400 mb-1">15+</div>
                <div className="text-gray-400 text-sm">Years Experience</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-400 mb-1">2000+</div>
                <div className="text-gray-400 text-sm">Students Graduated</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-yellow-400 mb-1">95%</div>
                <div className="text-gray-400 text-sm">Success Rate</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-purple-400 mb-1">50+</div>
                <div className="text-gray-400 text-sm">Expert Teachers</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator className="bg-gray-800" />

      {/* Bottom Footer */}
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm text-center md:text-left">
              <p>
                © {currentYear} Excellence Academy, Biratnagar. All rights reserved.
              </p>
              <p className="mt-1">
                Designed with <Heart className="w-4 h-4 text-red-500 inline mx-1" /> for student success.
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-400">
              <button className="hover:text-white transition-colors">
                Privacy Policy
              </button>
              <button className="hover:text-white transition-colors">
                Terms of Service
              </button>
              <button className="hover:text-white transition-colors">
                Admission Policy
              </button>
            </div>
          </div>

          {/* Emergency Contact */}
          <div className="mt-4 pt-4 border-t border-gray-800 text-center">
            <p className="text-gray-400 text-sm">
              Emergency Contact: <span className="text-red-400 font-medium">+977-**********</span> (24/7)
            </p>
          </div>
        </div>
      </div>

      {/* Back to Top Button */}
      <button
        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
        className="fixed bottom-8 right-8 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all duration-300 hover:scale-110 z-50"
        aria-label="Back to top"
      >
        <div className="w-4 h-4 border-t-2 border-r-2 border-white transform rotate-[-45deg] mx-auto"></div>
      </button>
    </footer>
  );
}
