"use client";

import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  GraduationCap,
  Award,
  BookOpen,
  Users,
  Star,
  Calendar,
  Mail,
  Phone
} from "lucide-react";

const faculty = [
  {
    id: 1,
    name: "Dr. <PERSON><PERSON>",
    designation: "Principal & Mathematics Expert",
    qualification: "Ph.D. in Mathematics, M.Sc. Mathematics",
    experience: "15+ Years",
    subjects: ["Mathematics", "Optional Mathematics", "Engineering Math"],
    specialization: "Advanced Calculus, Algebra, Engineering Entrance",
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face",
    achievements: ["Best Teacher Award 2023", "Published 5 Research Papers", "IOE Entrance Expert"],
    rating: 4.9,
    studentsCount: 500,
    successRate: "98%"
  },
  {
    id: 2,
    name: "Mrs. <PERSON><PERSON>",
    designation: "Physics Department Head",
    qualification: "M.Sc. Physics, B.Ed.",
    experience: "12+ Years",
    subjects: ["Physics", "Applied Physics", "Nuclear Physics"],
    specialization: "Mechanics, Thermodynamics, Modern Physics",
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face",
    achievements: ["Excellence in Teaching Award", "Physics Olympiad Trainer", "Research in Quantum Physics"],
    rating: 4.8,
    studentsCount: 350,
    successRate: "96%"
  },
  {
    id: 3,
    name: "Mr. Prakash Limbu",
    designation: "Chemistry Expert",
    qualification: "M.Sc. Chemistry, M.Ed.",
    experience: "10+ Years",
    subjects: ["Chemistry", "Organic Chemistry", "Biochemistry"],
    specialization: "Organic Reactions, Chemical Bonding, Lab Techniques",
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face",
    achievements: ["Chemistry Innovation Award", "Lab Safety Expert", "Medical Entrance Specialist"],
    rating: 4.9,
    studentsCount: 400,
    successRate: "97%"
  },
  {
    id: 4,
    name: "Dr. Kamala Devi Rai",
    designation: "Biology & Life Sciences",
    qualification: "Ph.D. in Botany, M.Sc. Biology",
    experience: "14+ Years",
    subjects: ["Biology", "Botany", "Zoology", "Human Physiology"],
    specialization: "Plant Biology, Human Anatomy, Medical Entrance",
    image: "https://images.unsplash.com/photo-**********-2b71ea197ec2?w=300&h=300&fit=crop&crop=face",
    achievements: ["Medical College Lecturer", "MBBS Entrance Expert", "Biology Research Publications"],
    rating: 4.9,
    studentsCount: 320,
    successRate: "95%"
  },
  {
    id: 5,
    name: "Mr. Dipesh Shrestha",
    designation: "English & Communication",
    qualification: "M.A. English Literature, TESOL Certified",
    experience: "8+ Years",
    subjects: ["English", "Communication Skills", "Creative Writing"],
    specialization: "Grammar, Literature, Speaking Skills, IELTS Prep",
    image: "https://images.unsplash.com/photo-**********-0b93528c311a?w=300&h=300&fit=crop&crop=face",
    achievements: ["IELTS Certified Trainer", "Creative Writing Workshop Leader", "English Debate Coach"],
    rating: 4.7,
    studentsCount: 280,
    successRate: "94%"
  },
  {
    id: 6,
    name: "Mrs. Anita Gurung",
    designation: "Accountancy & Business Studies",
    qualification: "M.Com, CA (Semi-Qualified), MBA",
    experience: "9+ Years",
    subjects: ["Accountancy", "Business Studies", "Economics"],
    specialization: "Financial Accounting, Business Management, Economics",
    image: "https://images.unsplash.com/photo-*************-6461ffad8d80?w=300&h=300&fit=crop&crop=face",
    achievements: ["CA Institute Trainer", "Business Plan Competition Judge", "Economics Research"],
    rating: 4.8,
    studentsCount: 250,
    successRate: "96%"
  }
];

export default function FacultySection() {
  return (
    <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4 text-blue-600 bg-blue-100">
              Our Faculty
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Meet Our Expert Instructors
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Our highly qualified and experienced faculty members are the backbone of our success. 
              Each instructor brings years of expertise and a passion for teaching that inspires students to excel.
            </p>
          </div>

          {/* Faculty Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {faculty.map((instructor) => (
              <Card key={instructor.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bg-white border-0">
                <div className="relative">
                  <Image
                    src={instructor.image}
                    alt={instructor.name}
                    width={300}
                    height={256}
                    className="w-full h-64 object-cover"
                  />
                  <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center">
                    <Star className="w-4 h-4 text-yellow-500 mr-1" />
                    <span className="text-sm font-semibold">{instructor.rating}</span>
                  </div>
                </div>
                
                <CardContent className="p-6">
                  <div className="mb-4">
                    <h3 className="text-xl font-bold text-gray-900 mb-1">
                      {instructor.name}
                    </h3>
                    <p className="text-blue-600 font-medium mb-2">
                      {instructor.designation}
                    </p>
                    <p className="text-gray-600 text-sm mb-3">
                      {instructor.qualification}
                    </p>
                  </div>

                  <div className="space-y-3 mb-4">
                    <div className="flex items-center text-sm text-gray-600">
                      <Calendar className="w-4 h-4 mr-2 text-green-600" />
                      <span>{instructor.experience} Experience</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Users className="w-4 h-4 mr-2 text-blue-600" />
                      <span>{instructor.studentsCount}+ Students Taught</span>
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Award className="w-4 h-4 mr-2 text-purple-600" />
                      <span>{instructor.successRate} Success Rate</span>
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Subjects:</h4>
                    <div className="flex flex-wrap gap-1">
                      {instructor.subjects.slice(0, 3).map((subject, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {subject}
                        </Badge>
                      ))}
                      {instructor.subjects.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{instructor.subjects.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-900 mb-2">Specialization:</h4>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {instructor.specialization}
                    </p>
                  </div>

                  <div className="mb-6">
                    <h4 className="font-semibold text-gray-900 mb-2">Key Achievements:</h4>
                    <ul className="space-y-1">
                      {instructor.achievements.slice(0, 2).map((achievement, index) => (
                        <li key={index} className="flex items-center text-sm text-gray-600">
                          <div className="w-1 h-1 bg-blue-600 rounded-full mr-2"></div>
                          {achievement}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <Button 
                    className="w-full" 
                    variant="outline"
                    onClick={() => {
                      const contactSection = document.getElementById('contact');
                      contactSection?.scrollIntoView({ behavior: 'smooth' });
                    }}
                  >
                    <Mail className="w-4 h-4 mr-2" />
                    Contact Instructor
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Faculty Stats */}
          <div className="bg-white rounded-2xl p-8 shadow-lg mb-16">
            <h3 className="text-2xl font-bold text-gray-900 text-center mb-8">
              Our Faculty Excellence
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <GraduationCap className="w-8 h-8 text-blue-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">50+</div>
                <div className="text-gray-600 text-sm">Expert Teachers</div>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Award className="w-8 h-8 text-green-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">25+</div>
                <div className="text-gray-600 text-sm">Awards Won</div>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BookOpen className="w-8 h-8 text-purple-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">100+</div>
                <div className="text-gray-600 text-sm">Research Papers</div>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Users className="w-8 h-8 text-yellow-600" />
                </div>
                <div className="text-3xl font-bold text-gray-900 mb-2">2000+</div>
                <div className="text-gray-600 text-sm">Students Mentored</div>
              </div>
            </div>
          </div>

          {/* Join Our Team CTA */}
          <div className="bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 text-white text-center">
            <h3 className="text-2xl font-bold mb-4">
              Passionate About Teaching?
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              We&apos;re always looking for dedicated educators to join our team. If you have the expertise
              and passion for teaching, we&apos;d love to hear from you.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                size="lg"
                className="bg-white text-blue-600 hover:bg-gray-100"
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  contactSection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                <Mail className="w-4 h-4 mr-2" />
                Apply to Teach
              </Button>
              <Button 
                size="lg"
                variant="outline"
                className="border-white text-white hover:bg-white hover:text-blue-600"
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  contactSection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                <Phone className="w-4 h-4 mr-2" />
                Call Us
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
