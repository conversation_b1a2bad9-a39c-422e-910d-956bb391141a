{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Button } from \"@/components/ui/button\";\nimport { \n  GraduationCap, \n  Menu, \n  X, \n  Phone,\n  Mail,\n  MapPin\n} from \"lucide-react\";\n\nconst navLinks = [\n  { name: \"Home\", href: \"#home\" },\n  { name: \"About\", href: \"#about\" },\n  { name: \"Courses\", href: \"#courses\" },\n  { name: \"Faculty\", href: \"#faculty\" },\n  { name: \"Why Choose Us\", href: \"#why-choose-us\" },\n  { name: \"Gallery\", href: \"#gallery\" },\n  { name: \"Testimonials\", href: \"#testimonials\" },\n  { name: \"Contact\", href: \"#contact\" }\n];\n\nexport default function Navbar() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const scrollToSection = (href: string) => {\n    setIsOpen(false);\n    if (href === \"#home\") {\n      window.scrollTo({ top: 0, behavior: 'smooth' });\n    } else {\n      const element = document.getElementById(href.substring(1));\n      element?.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  return (\n    <>\n      {/* Top Bar */}\n      <div className=\"bg-blue-900 text-white py-2 text-sm\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0\">\n            <div className=\"flex flex-col md:flex-row items-center space-y-1 md:space-y-0 md:space-x-6\">\n              <div className=\"flex items-center\">\n                <Phone className=\"w-3 h-3 mr-2\" />\n                <span>+977-21-123456</span>\n              </div>\n              <div className=\"flex items-center\">\n                <Mail className=\"w-3 h-3 mr-2\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center\">\n                <MapPin className=\"w-3 h-3 mr-2\" />\n                <span>Main Road, Biratnagar</span>\n              </div>\n            </div>\n            <div className=\"text-center md:text-right\">\n              <span className=\"text-yellow-300\">📞 Emergency: +977-********** (24/7)</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Navbar */}\n      <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? 'bg-white/95 backdrop-blur-md shadow-lg' \n          : 'bg-transparent'\n      }`} style={{ top: isScrolled ? '0' : '40px' }}>\n        <div className=\"container mx-auto px-4\">\n          <div className=\"flex items-center justify-between h-16\">\n            {/* Logo */}\n            <div className=\"flex items-center space-x-3\">\n              <div className={`w-10 h-10 rounded-full flex items-center justify-center transition-colors ${\n                isScrolled ? 'bg-blue-600' : 'bg-white/20'\n              }`}>\n                <GraduationCap className={`w-6 h-6 ${\n                  isScrolled ? 'text-white' : 'text-white'\n                }`} />\n              </div>\n              <div>\n                <h1 className={`text-xl font-bold transition-colors ${\n                  isScrolled ? 'text-gray-900' : 'text-white'\n                }`}>\n                  Excellence Academy\n                </h1>\n                <p className={`text-xs transition-colors ${\n                  isScrolled ? 'text-gray-600' : 'text-blue-200'\n                }`}>\n                  Biratnagar's Premier Institute\n                </p>\n              </div>\n            </div>\n\n            {/* Desktop Navigation */}\n            <div className=\"hidden lg:flex items-center space-x-8\">\n              {navLinks.map((link) => (\n                <button\n                  key={link.name}\n                  onClick={() => scrollToSection(link.href)}\n                  className={`font-medium transition-colors hover:scale-105 transform duration-200 ${\n                    isScrolled \n                      ? 'text-gray-700 hover:text-blue-600' \n                      : 'text-white hover:text-yellow-300'\n                  }`}\n                >\n                  {link.name}\n                </button>\n              ))}\n            </div>\n\n            {/* CTA Button & Mobile Menu */}\n            <div className=\"flex items-center space-x-4\">\n              <Button\n                onClick={() => scrollToSection('#contact')}\n                className={`hidden md:flex transition-all duration-200 ${\n                  isScrolled\n                    ? 'bg-blue-600 hover:bg-blue-700 text-white'\n                    : 'bg-yellow-500 hover:bg-yellow-600 text-gray-900'\n                }`}\n              >\n                Enroll Now\n              </Button>\n\n              {/* Mobile Menu Button */}\n              <button\n                onClick={() => setIsOpen(!isOpen)}\n                className={`lg:hidden p-2 rounded-md transition-colors ${\n                  isScrolled \n                    ? 'text-gray-700 hover:bg-gray-100' \n                    : 'text-white hover:bg-white/20'\n                }`}\n              >\n                {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Menu */}\n        {isOpen && (\n          <div className=\"lg:hidden bg-white border-t shadow-lg\">\n            <div className=\"container mx-auto px-4 py-4\">\n              <div className=\"flex flex-col space-y-4\">\n                {navLinks.map((link) => (\n                  <button\n                    key={link.name}\n                    onClick={() => scrollToSection(link.href)}\n                    className=\"text-left text-gray-700 hover:text-blue-600 font-medium py-2 transition-colors\"\n                  >\n                    {link.name}\n                  </button>\n                ))}\n                <div className=\"pt-4 border-t\">\n                  <Button\n                    onClick={() => scrollToSection('#contact')}\n                    className=\"w-full bg-blue-600 hover:bg-blue-700 text-white\"\n                  >\n                    Enroll Now\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </nav>\n\n      {/* Spacer to prevent content from hiding behind fixed navbar */}\n      <div className=\"h-24\"></div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAaA,MAAM,WAAW;IACf;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAiB,MAAM;IAAiB;IAChD;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,UAAU;QACV,IAAI,SAAS,SAAS;YACpB,OAAO,QAAQ,CAAC;gBAAE,KAAK;gBAAG,UAAU;YAAS;QAC/C,OAAO;YACL,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,SAAS,CAAC;YACvD,SAAS,eAAe;gBAAE,UAAU;YAAS;QAC/C;IACF;IAEA,qBACE;;0BAEE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAGV,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1C,8OAAC;gBAAI,WAAW,CAAC,4DAA4D,EAC3E,aACI,2CACA,kBACJ;gBAAE,OAAO;oBAAE,KAAK,aAAa,MAAM;gBAAO;;kCAC1C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,0EAA0E,EACzF,aAAa,gBAAgB,eAC7B;sDACA,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAW,CAAC,QAAQ,EACjC,aAAa,eAAe,cAC5B;;;;;;;;;;;sDAEJ,8OAAC;;8DACC,8OAAC;oDAAG,WAAW,CAAC,oCAAoC,EAClD,aAAa,kBAAkB,cAC/B;8DAAE;;;;;;8DAGJ,8OAAC;oDAAE,WAAW,CAAC,0BAA0B,EACvC,aAAa,kBAAkB,iBAC/B;8DAAE;;;;;;;;;;;;;;;;;;8CAOR,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;4CAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;4CACxC,WAAW,CAAC,qEAAqE,EAC/E,aACI,sCACA,oCACJ;sDAED,KAAK,IAAI;2CARL,KAAK,IAAI;;;;;;;;;;8CAcpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,2CAA2C,EACrD,aACI,6CACA,mDACJ;sDACH;;;;;;sDAKD,8OAAC;4CACC,SAAS,IAAM,UAAU,CAAC;4CAC1B,WAAW,CAAC,2CAA2C,EACrD,aACI,oCACA,gCACJ;sDAED,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAAe,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAO7D,wBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC;4CAEC,SAAS,IAAM,gBAAgB,KAAK,IAAI;4CACxC,WAAU;sDAET,KAAK,IAAI;2CAJL,KAAK,IAAI;;;;;kDAOlB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,gBAAgB;4CAC/B,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWb,8OAAC;gBAAI,WAAU;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 467, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/HeroSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { GraduationCap, BookOpen, Users, Award } from \"lucide-react\";\n\nconst heroImages = [\n  {\n    url: \"https://images.unsplash.com/photo-1523050854058-8df90110c9d1?w=1920&h=1080&fit=crop\",\n    alt: \"Students studying in modern classroom\"\n  },\n  {\n    url: \"https://images.unsplash.com/photo-1509062522246-3755977927d7?w=1920&h=1080&fit=crop\",\n    alt: \"Interactive learning session\"\n  },\n  {\n    url: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=1920&h=1080&fit=crop\",\n    alt: \"Students collaborating on projects\"\n  }\n];\n\nexport default function HeroSection() {\n  const scrollToContact = () => {\n    const contactSection = document.getElementById('contact');\n    contactSection?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0\">\n        <img\n          src=\"https://images.unsplash.com/photo-1523050854058-8df90110c9d1?w=1920&h=1080&fit=crop\"\n          alt=\"Students studying in classroom\"\n          className=\"w-full h-full object-cover\"\n        />\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-900/85 via-blue-800/80 to-indigo-900/85\"></div>\n      </div>\n\n      {/* Background Pattern Overlay */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n          backgroundSize: '60px 60px'\n        }}></div>\n      </div>\n      \n      {/* Floating Elements */}\n      <div className=\"absolute top-20 left-10 animate-bounce\">\n        <BookOpen className=\"w-8 h-8 text-blue-300 opacity-60\" />\n      </div>\n      <div className=\"absolute top-32 right-20 animate-pulse\">\n        <GraduationCap className=\"w-10 h-10 text-indigo-300 opacity-60\" />\n      </div>\n      <div className=\"absolute bottom-32 left-20 animate-bounce delay-1000\">\n        <Users className=\"w-6 h-6 text-blue-200 opacity-60\" />\n      </div>\n      <div className=\"absolute bottom-20 right-10 animate-pulse delay-500\">\n        <Award className=\"w-8 h-8 text-indigo-200 opacity-60\" />\n      </div>\n\n      <div className=\"container mx-auto px-4 text-center relative z-10\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Main Heading */}\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\">\n            Excel in Your\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500\">\n              Academic Journey\n            </span>\n          </h1>\n          \n          {/* Subheading */}\n          <p className=\"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed\">\n            Join Biratnagar's premier coaching institute and unlock your potential with expert guidance, \n            personalized attention, and proven results in SEE, +2, and entrance examinations.\n          </p>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 max-w-2xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">15+</div>\n              <div className=\"text-sm text-blue-200\">Years Experience</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">2000+</div>\n              <div className=\"text-sm text-blue-200\">Students Taught</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">95%</div>\n              <div className=\"text-sm text-blue-200\">Success Rate</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">50+</div>\n              <div className=\"text-sm text-blue-200\">Expert Teachers</div>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button \n              size=\"lg\" \n              className=\"bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white font-semibold px-8 py-3 text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\"\n              onClick={scrollToContact}\n            >\n              Get Enrolled Now\n            </Button>\n            <Button \n              size=\"lg\" \n              variant=\"outline\" \n              className=\"border-2 border-white text-white hover:bg-white hover:text-blue-900 font-semibold px-8 py-3 text-lg transition-all duration-200\"\n              onClick={() => {\n                const aboutSection = document.getElementById('about');\n                aboutSection?.scrollIntoView({ behavior: 'smooth' });\n              }}\n            >\n              Learn More\n            </Button>\n          </div>\n\n          {/* Trust Indicators */}\n          <div className=\"mt-16 pt-8 border-t border-blue-700\">\n            <p className=\"text-blue-200 mb-4\">Trusted by students from:</p>\n            <div className=\"flex flex-wrap justify-center items-center gap-8 text-blue-300\">\n              <span className=\"text-sm\">Biratnagar Metropolitan</span>\n              <span className=\"text-sm\">•</span>\n              <span className=\"text-sm\">Morang District</span>\n              <span className=\"text-sm\">•</span>\n              <span className=\"text-sm\">Province 1</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AAAA;AAJA;;;;AAMA,MAAM,aAAa;IACjB;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;IACA;QACE,KAAK;QACL,KAAK;IACP;CACD;AAEc,SAAS;IACtB,MAAM,kBAAkB;QACtB,MAAM,iBAAiB,SAAS,cAAc,CAAC;QAC/C,gBAAgB,eAAe;YAAE,UAAU;QAAS;IACtD;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAmB,OAAO;wBACvC,iBAAiB,CAAC,gQAAgQ,CAAC;wBACnR,gBAAgB;oBAClB;;;;;;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAEtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;0BAE3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAEnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAGnB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAG,WAAU;;gCAAoD;8CAEhE,8OAAC;oCAAK,WAAU;8CAAqF;;;;;;;;;;;;sCAMvG,8OAAC;4BAAE,WAAU;sCAA2E;;;;;;sCAMxF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,8OAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;8CACV;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;wCACP,MAAM,eAAe,SAAS,cAAc,CAAC;wCAC7C,cAAc,eAAe;4CAAE,UAAU;wCAAS;oCACpD;8CACD;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/AboutSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Target, Heart, BookOpen, Users, Award, Clock } from \"lucide-react\";\n\nexport default function AboutSection() {\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              About Excellence Academy\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Nurturing Academic Excellence Since 2009\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Located in the heart of Biratnagar, we have been the trusted partner in thousands of students' \n              academic journeys, helping them achieve their dreams through quality education and personalized guidance.\n            </p>\n          </div>\n\n          {/* Main Content Grid */}\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center mb-16\">\n            {/* Left Content */}\n            <div className=\"space-y-8 order-2 lg:order-1\">\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 flex items-center\">\n                  <Target className=\"w-6 h-6 text-blue-600 mr-3\" />\n                  Our Mission\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  To provide world-class education that empowers students to excel in their academic pursuits \n                  and develop into confident, capable individuals ready to face future challenges. We believe \n                  every student has unique potential that deserves to be nurtured and developed.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 flex items-center\">\n                  <Heart className=\"w-6 h-6 text-red-500 mr-3\" />\n                  Our Values\n                </h3>\n                <ul className=\"space-y-2 text-gray-600\">\n                  <li className=\"flex items-center\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                    Excellence in teaching and learning\n                  </li>\n                  <li className=\"flex items-center\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                    Individual attention to every student\n                  </li>\n                  <li className=\"flex items-center\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                    Integrity and ethical practices\n                  </li>\n                  <li className=\"flex items-center\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                    Continuous innovation in education\n                  </li>\n                </ul>\n              </div>\n\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 flex items-center\">\n                  <BookOpen className=\"w-6 h-6 text-green-600 mr-3\" />\n                  Teaching Approach\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Our proven methodology combines traditional teaching excellence with modern educational \n                  techniques. We focus on conceptual clarity, regular practice, and continuous assessment \n                  to ensure comprehensive learning and outstanding results.\n                </p>\n              </div>\n            </div>\n\n            {/* Right Content - Image and Stats */}\n            <div className=\"order-1 lg:order-2 space-y-6\">\n              {/* Hero Image */}\n              <div className=\"relative rounded-2xl overflow-hidden shadow-2xl\">\n                <img\n                  src=\"https://images.unsplash.com/photo-1509062522246-3755977927d7?w=600&h=400&fit=crop\"\n                  alt=\"Students in modern classroom\"\n                  className=\"w-full h-80 object-cover\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/30 to-transparent\"></div>\n                <div className=\"absolute bottom-4 left-4 text-white\">\n                  <p className=\"text-sm font-medium\">Excellence in Education Since 2009</p>\n                </div>\n              </div>\n\n              {/* Stats Cards */}\n              <div className=\"grid grid-cols-2 gap-4\">\n                <Card className=\"text-center p-4 hover:shadow-lg transition-shadow\">\n                  <CardContent className=\"p-0\">\n                    <Users className=\"w-8 h-8 text-blue-600 mx-auto mb-2\" />\n                    <div className=\"text-2xl font-bold text-gray-900 mb-1\">2000+</div>\n                    <div className=\"text-gray-600 text-sm\">Students</div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"text-center p-4 hover:shadow-lg transition-shadow\">\n                  <CardContent className=\"p-0\">\n                    <Award className=\"w-8 h-8 text-yellow-600 mx-auto mb-2\" />\n                    <div className=\"text-2xl font-bold text-gray-900 mb-1\">95%</div>\n                    <div className=\"text-gray-600 text-sm\">Success Rate</div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"text-center p-4 hover:shadow-lg transition-shadow\">\n                  <CardContent className=\"p-0\">\n                    <Clock className=\"w-8 h-8 text-green-600 mx-auto mb-2\" />\n                    <div className=\"text-2xl font-bold text-gray-900 mb-1\">15+</div>\n                    <div className=\"text-gray-600 text-sm\">Years</div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"text-center p-4 hover:shadow-lg transition-shadow\">\n                  <CardContent className=\"p-0\">\n                    <BookOpen className=\"w-8 h-8 text-purple-600 mx-auto mb-2\" />\n                    <div className=\"text-2xl font-bold text-gray-900 mb-1\">50+</div>\n                    <div className=\"text-gray-600 text-sm\">Teachers</div>\n                  </CardContent>\n                </Card>\n              </div>\n            </div>\n          </div>\n\n          {/* Experience Timeline */}\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">Our Journey</h3>\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl font-bold text-blue-600\">2009</span>\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Foundation</h4>\n                <p className=\"text-gray-600 text-sm\">Started with a vision to provide quality education in Biratnagar</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl font-bold text-green-600\">2015</span>\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Expansion</h4>\n                <p className=\"text-gray-600 text-sm\">Expanded to include entrance exam preparation courses</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl font-bold text-purple-600\">2024</span>\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Excellence</h4>\n                <p className=\"text-gray-600 text-sm\">Recognized as the leading coaching institute in the region</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAA+B;;;;;;;0DAGnD,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAO/C,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;0DAGjD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAA8C;;;;;;;kEAG/D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAA8C;;;;;;;kEAG/D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAA8C;;;;;;;kEAG/D,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAI,WAAU;;;;;;4DAA8C;;;;;;;;;;;;;;;;;;;kDAMnE,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC,8MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;0DAGtD,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CASjD,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAE,WAAU;8DAAsB;;;;;;;;;;;;;;;;;kDAKvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAI3C,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAI3C,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;0DAI3C,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC,8MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC;4DAAI,WAAU;sEAAwC;;;;;;sEACvD,8OAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;0DAErD,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;0DAEtD,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAAqC;;;;;;;;;;;0DAEvD,8OAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/CoursesSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { \n  GraduationCap, \n  BookOpen, \n  Calculator, \n  Microscope, \n  Building, \n  Users, \n  Clock,\n  Star,\n  CheckCircle\n} from \"lucide-react\";\n\nconst courses = [\n  {\n    id: 1,\n    title: \"SEE Preparation\",\n    subtitle: \"Secondary Education Examination\",\n    icon: GraduationCap,\n    image: \"https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=250&fit=crop\",\n    duration: \"1 Year\",\n    students: \"200+\",\n    rating: \"4.9\",\n    subjects: [\"Mathematics\", \"Science\", \"English\", \"Social Studies\", \"Nepali\", \"Optional Math\"],\n    features: [\n      \"Comprehensive syllabus coverage\",\n      \"Regular mock tests\",\n      \"Individual doubt clearing sessions\",\n      \"Study materials included\"\n    ],\n    color: \"blue\",\n    popular: true\n  },\n  {\n    id: 2,\n    title: \"+2 Science\",\n    subtitle: \"Physics, Chemistry, Biology & Mathematics\",\n    icon: Microscope,\n    image: \"https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=400&h=250&fit=crop\",\n    duration: \"2 Years\",\n    students: \"150+\",\n    rating: \"4.8\",\n    subjects: [\"Physics\", \"Chemistry\", \"Biology\", \"Mathematics\", \"English\"],\n    features: [\n      \"Lab practical sessions\",\n      \"Concept-based learning\",\n      \"Board exam preparation\",\n      \"Career guidance included\"\n    ],\n    color: \"green\",\n    popular: false\n  },\n  {\n    id: 3,\n    title: \"+2 Management\",\n    subtitle: \"Business Studies & Economics\",\n    icon: Building,\n    image: \"https://images.unsplash.com/photo-*************-c3d57bc86b40?w=400&h=250&fit=crop\",\n    duration: \"2 Years\",\n    students: \"120+\",\n    rating: \"4.7\",\n    subjects: [\"Accountancy\", \"Business Studies\", \"Economics\", \"Mathematics\", \"English\"],\n    features: [\n      \"Practical case studies\",\n      \"Industry exposure\",\n      \"Project-based learning\",\n      \"Internship opportunities\"\n    ],\n    color: \"purple\",\n    popular: false\n  },\n  {\n    id: 4,\n    title: \"Engineering Entrance\",\n    subtitle: \"IOE, Pulchowk, Thapathali\",\n    icon: Calculator,\n    image: \"https://images.unsplash.com/photo-*************-a6a2a5aee158?w=400&h=250&fit=crop\",\n    duration: \"6 Months\",\n    students: \"80+\",\n    rating: \"4.9\",\n    subjects: [\"Physics\", \"Chemistry\", \"Mathematics\"],\n    features: [\n      \"Previous year questions\",\n      \"Time management techniques\",\n      \"Weekly assessments\",\n      \"Rank prediction tests\"\n    ],\n    color: \"orange\",\n    popular: true\n  },\n  {\n    id: 5,\n    title: \"Medical Entrance\",\n    subtitle: \"MBBS, BDS, Nursing\",\n    icon: BookOpen,\n    image: \"https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=250&fit=crop\",\n    duration: \"6 Months\",\n    students: \"60+\",\n    rating: \"4.8\",\n    subjects: [\"Biology\", \"Chemistry\", \"Physics\"],\n    features: [\n      \"NEET pattern preparation\",\n      \"Medical terminology focus\",\n      \"Diagram practice sessions\",\n      \"Interview preparation\"\n    ],\n    color: \"red\",\n    popular: false\n  },\n  {\n    id: 6,\n    title: \"Foundation Course\",\n    subtitle: \"Class 8, 9 & 10\",\n    icon: Users,\n    image: \"https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=250&fit=crop\",\n    duration: \"Flexible\",\n    students: \"300+\",\n    rating: \"4.6\",\n    subjects: [\"All Core Subjects\", \"Skill Development\", \"Personality Development\"],\n    features: [\n      \"Strong foundation building\",\n      \"Interactive learning methods\",\n      \"Parent-teacher meetings\",\n      \"Progress tracking\"\n    ],\n    color: \"indigo\",\n    popular: false\n  }\n];\n\nconst getColorClasses = (color: string) => {\n  const colors = {\n    blue: \"border-blue-200 bg-blue-50 text-blue-600\",\n    green: \"border-green-200 bg-green-50 text-green-600\",\n    purple: \"border-purple-200 bg-purple-50 text-purple-600\",\n    orange: \"border-orange-200 bg-orange-50 text-orange-600\",\n    red: \"border-red-200 bg-red-50 text-red-600\",\n    indigo: \"border-indigo-200 bg-indigo-50 text-indigo-600\"\n  };\n  return colors[color as keyof typeof colors] || colors.blue;\n};\n\nexport default function CoursesSection() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Our Courses\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Comprehensive Academic Programs\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Choose from our wide range of courses designed to help you excel in your academic journey. \n              Each program is carefully crafted with expert guidance and proven methodologies.\n            </p>\n          </div>\n\n          {/* Courses Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {courses.map((course) => {\n              const IconComponent = course.icon;\n              return (\n                <Card\n                  key={course.id}\n                  className=\"group relative overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-0\"\n                >\n                  \n                  {/* Course Image */}\n                  <div className=\"relative h-48 overflow-hidden\">\n                    <img\n                      src={course.image}\n                      alt={course.title}\n                      className=\"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\"></div>\n                    <div className={`absolute top-4 left-4 w-12 h-12 rounded-full border-2 border-white bg-white/90 backdrop-blur-sm flex items-center justify-center`}>\n                      <IconComponent className=\"w-6 h-6 text-gray-700\" />\n                    </div>\n                    {course.popular && (\n                      <div className=\"absolute top-4 right-4\">\n                        <Badge className=\"bg-yellow-500 text-white\">\n                          <Star className=\"w-3 h-3 mr-1\" />\n                          Popular\n                        </Badge>\n                      </div>\n                    )}\n                  </div>\n\n                  <CardHeader className=\"pb-4\">\n                    \n                    <CardTitle className=\"text-xl font-bold text-gray-900 mb-2\">\n                      {course.title}\n                    </CardTitle>\n                    <p className=\"text-gray-600 text-sm mb-4\">{course.subtitle}</p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <Clock className=\"w-4 h-4 mr-1\" />\n                        {course.duration}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Users className=\"w-4 h-4 mr-1\" />\n                        {course.students}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Star className=\"w-4 h-4 mr-1 text-yellow-500\" />\n                        {course.rating}\n                      </div>\n                    </div>\n                  </CardHeader>\n                  \n                  <CardContent className=\"pt-0\">\n                    <div className=\"mb-4\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Subjects Covered:</h4>\n                      <div className=\"flex flex-wrap gap-1\">\n                        {course.subjects.slice(0, 3).map((subject, index) => (\n                          <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                            {subject}\n                          </Badge>\n                        ))}\n                        {course.subjects.length > 3 && (\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            +{course.subjects.length - 3} more\n                          </Badge>\n                        )}\n                      </div>\n                    </div>\n                    \n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Key Features:</h4>\n                      <ul className=\"space-y-1\">\n                        {course.features.slice(0, 3).map((feature, index) => (\n                          <li key={index} className=\"flex items-center text-sm text-gray-600\">\n                            <CheckCircle className=\"w-3 h-3 text-green-500 mr-2 flex-shrink-0\" />\n                            {feature}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                    \n                    <Button \n                      className=\"w-full\" \n                      variant={course.popular ? \"default\" : \"outline\"}\n                      onClick={() => {\n                        const contactSection = document.getElementById('contact');\n                        contactSection?.scrollIntoView({ behavior: 'smooth' });\n                      }}\n                    >\n                      Enroll Now\n                    </Button>\n                  </CardContent>\n                </Card>\n              );\n            })}\n          </div>\n\n          {/* Additional Info */}\n          <div className=\"mt-16 text-center\">\n            <div className=\"bg-gray-50 rounded-2xl p-8\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Can't find the right course?\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                We also offer customized courses and one-on-one tutoring sessions. \n                Contact us to discuss your specific academic needs.\n              </p>\n              <Button \n                size=\"lg\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Contact Us for Custom Courses\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAiBA,MAAM,UAAU;IACd;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAe;YAAW;YAAW;YAAkB;YAAU;SAAgB;QAC5F,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,8MAAA,CAAA,aAAU;QAChB,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAW;YAAa;YAAW;YAAe;SAAU;QACvE,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAe;YAAoB;YAAa;YAAe;SAAU;QACpF,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,8MAAA,CAAA,aAAU;QAChB,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAW;YAAa;SAAc;QACjD,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAW;YAAa;SAAU;QAC7C,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAqB;YAAqB;SAA0B;QAC/E,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;CACD;AAED,MAAM,kBAAkB,CAAC;IACvB,MAAM,SAAS;QACb,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;AAC5D;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC;4BACZ,MAAM,gBAAgB,OAAO,IAAI;4BACjC,qBACE,8OAAC,gIAAA,CAAA,OAAI;gCAEH,WAAU;;kDAIV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,OAAO,KAAK;gDACjB,KAAK,OAAO,KAAK;gDACjB,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAW,CAAC,gIAAgI,CAAC;0DAChJ,cAAA,8OAAC;oDAAc,WAAU;;;;;;;;;;;4CAE1B,OAAO,OAAO,kBACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,WAAU;;sEACf,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;kDAOzC,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DAEpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,OAAO,KAAK;;;;;;0DAEf,8OAAC;gDAAE,WAAU;0DAA8B,OAAO,QAAQ;;;;;;0DAE1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,OAAO,QAAQ;;;;;;;kEAElB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,OAAO,QAAQ;;;;;;;kEAElB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,OAAO,MAAM;;;;;;;;;;;;;;;;;;;kDAKpB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;4DACZ,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC,iIAAA,CAAA,QAAK;oEAAa,SAAQ;oEAAU,WAAU;8EAC5C;mEADS;;;;;4DAIb,OAAO,QAAQ,CAAC,MAAM,GAAG,mBACxB,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;oEAAU;oEACzC,OAAO,QAAQ,CAAC,MAAM,GAAG;oEAAE;;;;;;;;;;;;;;;;;;;0DAMrC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;kEACX,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzC,8OAAC;gEAAe,WAAU;;kFACxB,8OAAC,2NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB;;+DAFM;;;;;;;;;;;;;;;;0DAQf,8OAAC,kIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,SAAS,OAAO,OAAO,GAAG,YAAY;gDACtC,SAAS;oDACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;oDAC/C,gBAAgB,eAAe;wDAAE,UAAU;oDAAS;gDACtD;0DACD;;;;;;;;;;;;;+BApFE,OAAO,EAAE;;;;;wBA0FpB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;wCACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;wCAC/C,gBAAgB,eAAe;4CAAE,UAAU;wCAAS;oCACtD;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/FacultySection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { \n  GraduationCap, \n  Award, \n  BookOpen, \n  Users,\n  Star,\n  Calendar,\n  MapPin,\n  Mail,\n  Phone\n} from \"lucide-react\";\n\nconst faculty = [\n  {\n    id: 1,\n    name: \"Dr. <PERSON>\",\n    designation: \"Principal & Mathematics Expert\",\n    qualification: \"Ph.D. in Mathematics, M.Sc. Mathematics\",\n    experience: \"15+ Years\",\n    subjects: [\"Mathematics\", \"Optional Mathematics\", \"Engineering Math\"],\n    specialization: \"Advanced Calculus, Algebra, Engineering Entrance\",\n    image: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face\",\n    achievements: [\"Best Teacher Award 2023\", \"Published 5 Research Papers\", \"IOE Entrance Expert\"],\n    rating: 4.9,\n    studentsCount: 500,\n    successRate: \"98%\"\n  },\n  {\n    id: 2,\n    name: \"Mrs. <PERSON><PERSON>\",\n    designation: \"Physics Department Head\",\n    qualification: \"M<PERSON>Sc. Physics, B.Ed.\",\n    experience: \"12+ Years\",\n    subjects: [\"Physics\", \"Applied Physics\", \"Nuclear Physics\"],\n    specialization: \"Mechanics, Thermodynamics, Modern Physics\",\n    image: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face\",\n    achievements: [\"Excellence in Teaching Award\", \"Physics Olympiad Trainer\", \"Research in Quantum Physics\"],\n    rating: 4.8,\n    studentsCount: 350,\n    successRate: \"96%\"\n  },\n  {\n    id: 3,\n    name: \"Mr. Prakash Limbu\",\n    designation: \"Chemistry Expert\",\n    qualification: \"M.Sc. Chemistry, M.Ed.\",\n    experience: \"10+ Years\",\n    subjects: [\"Chemistry\", \"Organic Chemistry\", \"Biochemistry\"],\n    specialization: \"Organic Reactions, Chemical Bonding, Lab Techniques\",\n    image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face\",\n    achievements: [\"Chemistry Innovation Award\", \"Lab Safety Expert\", \"Medical Entrance Specialist\"],\n    rating: 4.9,\n    studentsCount: 400,\n    successRate: \"97%\"\n  },\n  {\n    id: 4,\n    name: \"Dr. Kamala Devi Rai\",\n    designation: \"Biology & Life Sciences\",\n    qualification: \"Ph.D. in Botany, M.Sc. Biology\",\n    experience: \"14+ Years\",\n    subjects: [\"Biology\", \"Botany\", \"Zoology\", \"Human Physiology\"],\n    specialization: \"Plant Biology, Human Anatomy, Medical Entrance\",\n    image: \"https://images.unsplash.com/photo-**********-2b71ea197ec2?w=300&h=300&fit=crop&crop=face\",\n    achievements: [\"Medical College Lecturer\", \"MBBS Entrance Expert\", \"Biology Research Publications\"],\n    rating: 4.9,\n    studentsCount: 320,\n    successRate: \"95%\"\n  },\n  {\n    id: 5,\n    name: \"Mr. Dipesh Shrestha\",\n    designation: \"English & Communication\",\n    qualification: \"M.A. English Literature, TESOL Certified\",\n    experience: \"8+ Years\",\n    subjects: [\"English\", \"Communication Skills\", \"Creative Writing\"],\n    specialization: \"Grammar, Literature, Speaking Skills, IELTS Prep\",\n    image: \"https://images.unsplash.com/photo-**********-0b93528c311a?w=300&h=300&fit=crop&crop=face\",\n    achievements: [\"IELTS Certified Trainer\", \"Creative Writing Workshop Leader\", \"English Debate Coach\"],\n    rating: 4.7,\n    studentsCount: 280,\n    successRate: \"94%\"\n  },\n  {\n    id: 6,\n    name: \"Mrs. Anita Gurung\",\n    designation: \"Accountancy & Business Studies\",\n    qualification: \"M.Com, CA (Semi-Qualified), MBA\",\n    experience: \"9+ Years\",\n    subjects: [\"Accountancy\", \"Business Studies\", \"Economics\"],\n    specialization: \"Financial Accounting, Business Management, Economics\",\n    image: \"https://images.unsplash.com/photo-*************-6461ffad8d80?w=300&h=300&fit=crop&crop=face\",\n    achievements: [\"CA Institute Trainer\", \"Business Plan Competition Judge\", \"Economics Research\"],\n    rating: 4.8,\n    studentsCount: 250,\n    successRate: \"96%\"\n  }\n];\n\nexport default function FacultySection() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Our Faculty\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Meet Our Expert Instructors\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Our highly qualified and experienced faculty members are the backbone of our success. \n              Each instructor brings years of expertise and a passion for teaching that inspires students to excel.\n            </p>\n          </div>\n\n          {/* Faculty Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16\">\n            {faculty.map((instructor) => (\n              <Card key={instructor.id} className=\"overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 bg-white border-0\">\n                <div className=\"relative\">\n                  <img\n                    src={instructor.image}\n                    alt={instructor.name}\n                    className=\"w-full h-64 object-cover\"\n                  />\n                  <div className=\"absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center\">\n                    <Star className=\"w-4 h-4 text-yellow-500 mr-1\" />\n                    <span className=\"text-sm font-semibold\">{instructor.rating}</span>\n                  </div>\n                </div>\n                \n                <CardContent className=\"p-6\">\n                  <div className=\"mb-4\">\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-1\">\n                      {instructor.name}\n                    </h3>\n                    <p className=\"text-blue-600 font-medium mb-2\">\n                      {instructor.designation}\n                    </p>\n                    <p className=\"text-gray-600 text-sm mb-3\">\n                      {instructor.qualification}\n                    </p>\n                  </div>\n\n                  <div className=\"space-y-3 mb-4\">\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <Calendar className=\"w-4 h-4 mr-2 text-green-600\" />\n                      <span>{instructor.experience} Experience</span>\n                    </div>\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <Users className=\"w-4 h-4 mr-2 text-blue-600\" />\n                      <span>{instructor.studentsCount}+ Students Taught</span>\n                    </div>\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <Award className=\"w-4 h-4 mr-2 text-purple-600\" />\n                      <span>{instructor.successRate} Success Rate</span>\n                    </div>\n                  </div>\n\n                  <div className=\"mb-4\">\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">Subjects:</h4>\n                    <div className=\"flex flex-wrap gap-1\">\n                      {instructor.subjects.slice(0, 3).map((subject, index) => (\n                        <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                          {subject}\n                        </Badge>\n                      ))}\n                      {instructor.subjects.length > 3 && (\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          +{instructor.subjects.length - 3} more\n                        </Badge>\n                      )}\n                    </div>\n                  </div>\n\n                  <div className=\"mb-4\">\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">Specialization:</h4>\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">\n                      {instructor.specialization}\n                    </p>\n                  </div>\n\n                  <div className=\"mb-6\">\n                    <h4 className=\"font-semibold text-gray-900 mb-2\">Key Achievements:</h4>\n                    <ul className=\"space-y-1\">\n                      {instructor.achievements.slice(0, 2).map((achievement, index) => (\n                        <li key={index} className=\"flex items-center text-sm text-gray-600\">\n                          <div className=\"w-1 h-1 bg-blue-600 rounded-full mr-2\"></div>\n                          {achievement}\n                        </li>\n                      ))}\n                    </ul>\n                  </div>\n\n                  <Button \n                    className=\"w-full\" \n                    variant=\"outline\"\n                    onClick={() => {\n                      const contactSection = document.getElementById('contact');\n                      contactSection?.scrollIntoView({ behavior: 'smooth' });\n                    }}\n                  >\n                    <Mail className=\"w-4 h-4 mr-2\" />\n                    Contact Instructor\n                  </Button>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {/* Faculty Stats */}\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg mb-16\">\n            <h3 className=\"text-2xl font-bold text-gray-900 text-center mb-8\">\n              Our Faculty Excellence\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <GraduationCap className=\"w-8 h-8 text-blue-600\" />\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 mb-2\">50+</div>\n                <div className=\"text-gray-600 text-sm\">Expert Teachers</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <Award className=\"w-8 h-8 text-green-600\" />\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 mb-2\">25+</div>\n                <div className=\"text-gray-600 text-sm\">Awards Won</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <BookOpen className=\"w-8 h-8 text-purple-600\" />\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 mb-2\">100+</div>\n                <div className=\"text-gray-600 text-sm\">Research Papers</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <Users className=\"w-8 h-8 text-yellow-600\" />\n                </div>\n                <div className=\"text-3xl font-bold text-gray-900 mb-2\">2000+</div>\n                <div className=\"text-gray-600 text-sm\">Students Mentored</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Join Our Team CTA */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 text-white text-center\">\n            <h3 className=\"text-2xl font-bold mb-4\">\n              Passionate About Teaching?\n            </h3>\n            <p className=\"text-blue-100 mb-6 max-w-2xl mx-auto\">\n              We're always looking for dedicated educators to join our team. If you have the expertise \n              and passion for teaching, we'd love to hear from you.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button \n                size=\"lg\"\n                className=\"bg-white text-blue-600 hover:bg-gray-100\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                <Mail className=\"w-4 h-4 mr-2\" />\n                Apply to Teach\n              </Button>\n              <Button \n                size=\"lg\"\n                variant=\"outline\"\n                className=\"border-white text-white hover:bg-white hover:text-blue-600\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                <Phone className=\"w-4 h-4 mr-2\" />\n                Call Us\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAiBA,MAAM,UAAU;IACd;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,YAAY;QACZ,UAAU;YAAC;YAAe;YAAwB;SAAmB;QACrE,gBAAgB;QAChB,OAAO;QACP,cAAc;YAAC;YAA2B;YAA+B;SAAsB;QAC/F,QAAQ;QACR,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,YAAY;QACZ,UAAU;YAAC;YAAW;YAAmB;SAAkB;QAC3D,gBAAgB;QAChB,OAAO;QACP,cAAc;YAAC;YAAgC;YAA4B;SAA8B;QACzG,QAAQ;QACR,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,YAAY;QACZ,UAAU;YAAC;YAAa;YAAqB;SAAe;QAC5D,gBAAgB;QAChB,OAAO;QACP,cAAc;YAAC;YAA8B;YAAqB;SAA8B;QAChG,QAAQ;QACR,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,YAAY;QACZ,UAAU;YAAC;YAAW;YAAU;YAAW;SAAmB;QAC9D,gBAAgB;QAChB,OAAO;QACP,cAAc;YAAC;YAA4B;YAAwB;SAAgC;QACnG,QAAQ;QACR,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,YAAY;QACZ,UAAU;YAAC;YAAW;YAAwB;SAAmB;QACjE,gBAAgB;QAChB,OAAO;QACP,cAAc;YAAC;YAA2B;YAAoC;SAAuB;QACrG,QAAQ;QACR,eAAe;QACf,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,eAAe;QACf,YAAY;QACZ,UAAU;YAAC;YAAe;YAAoB;SAAY;QAC1D,gBAAgB;QAChB,OAAO;QACP,cAAc;YAAC;YAAwB;YAAmC;SAAqB;QAC/F,QAAQ;QACR,eAAe;QACf,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,2BACZ,8OAAC,gIAAA,CAAA,OAAI;gCAAqB,WAAU;;kDAClC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,WAAW,KAAK;gDACrB,KAAK,WAAW,IAAI;gDACpB,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,8OAAC;wDAAK,WAAU;kEAAyB,WAAW,MAAM;;;;;;;;;;;;;;;;;;kDAI9D,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,WAAW,IAAI;;;;;;kEAElB,8OAAC;wDAAE,WAAU;kEACV,WAAW,WAAW;;;;;;kEAEzB,8OAAC;wDAAE,WAAU;kEACV,WAAW,aAAa;;;;;;;;;;;;0DAI7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;;oEAAM,WAAW,UAAU;oEAAC;;;;;;;;;;;;;kEAE/B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;;oEAAM,WAAW,aAAa;oEAAC;;;;;;;;;;;;;kEAElC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,oMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;0EACjB,8OAAC;;oEAAM,WAAW,WAAW;oEAAC;;;;;;;;;;;;;;;;;;;0DAIlC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAI,WAAU;;4DACZ,WAAW,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBAC7C,8OAAC,iIAAA,CAAA,QAAK;oEAAa,SAAQ;oEAAU,WAAU;8EAC5C;mEADS;;;;;4DAIb,WAAW,QAAQ,CAAC,MAAM,GAAG,mBAC5B,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;oEAAU;oEACzC,WAAW,QAAQ,CAAC,MAAM,GAAG;oEAAE;;;;;;;;;;;;;;;;;;;0DAMzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,WAAW,cAAc;;;;;;;;;;;;0DAI9B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAG,WAAU;kEACX,WAAW,YAAY,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,aAAa,sBACrD,8OAAC;gEAAe,WAAU;;kFACxB,8OAAC;wEAAI,WAAU;;;;;;oEACd;;+DAFM;;;;;;;;;;;;;;;;0DAQf,8OAAC,kIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,SAAQ;gDACR,SAAS;oDACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;oDAC/C,gBAAgB,eAAe;wDAAE,UAAU;oDAAS;gDACtD;;kEAEA,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;+BApF5B,WAAW,EAAE;;;;;;;;;;kCA6F5B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;0DAE3B,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;kCAM7C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA0B;;;;;;0CAGxC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,SAAS;4CACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;4CAC/C,gBAAgB,eAAe;gDAAE,UAAU;4CAAS;wCACtD;;0DAEA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGnC,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS;4CACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;4CAC/C,gBAAgB,eAAe;gDAAE,UAAU;4CAAS;wCACtD;;0DAEA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD", "debugId": null}}, {"offset": {"line": 3030, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/WhyChooseUsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  Users, \n  Award, \n  Clock, \n  BookOpen, \n  Target, \n  TrendingUp,\n  CheckCircle,\n  Star,\n  Brain,\n  Shield,\n  Zap,\n  Heart\n} from \"lucide-react\";\n\nconst features = [\n  {\n    icon: Users,\n    title: \"Expert Faculty\",\n    description: \"Our team consists of highly qualified teachers with 10+ years of experience in their respective subjects.\",\n    stats: \"50+ Expert Teachers\",\n    color: \"blue\"\n  },\n  {\n    icon: Target,\n    title: \"Small Batch Sizes\",\n    description: \"We maintain small class sizes (15-20 students) to ensure personalized attention and better learning outcomes.\",\n    stats: \"Max 20 Students/Batch\",\n    color: \"green\"\n  },\n  {\n    icon: Clock,\n    title: \"Regular Assessments\",\n    description: \"Weekly tests, monthly evaluations, and mock exams to track progress and identify areas for improvement.\",\n    stats: \"Weekly Tests\",\n    color: \"purple\"\n  },\n  {\n    icon: TrendingUp,\n    title: \"Proven Results\",\n    description: \"95% of our students achieve their target scores with many securing top ranks in their respective examinations.\",\n    stats: \"95% Success Rate\",\n    color: \"orange\"\n  },\n  {\n    icon: BookOpen,\n    title: \"Comprehensive Study Material\",\n    description: \"Well-researched notes, practice papers, and reference materials designed by our expert faculty.\",\n    stats: \"Complete Study Package\",\n    color: \"red\"\n  },\n  {\n    icon: Brain,\n    title: \"Modern Teaching Methods\",\n    description: \"Interactive learning, visual aids, and technology-enhanced teaching for better concept understanding.\",\n    stats: \"Interactive Learning\",\n    color: \"indigo\"\n  },\n  {\n    icon: Shield,\n    title: \"Safe Learning Environment\",\n    description: \"Clean, well-ventilated classrooms with proper safety measures and a conducive learning atmosphere.\",\n    stats: \"Safe & Secure\",\n    color: \"teal\"\n  },\n  {\n    icon: Heart,\n    title: \"Individual Care\",\n    description: \"Personal mentoring, career guidance, and emotional support to help students overcome challenges.\",\n    stats: \"Personal Mentoring\",\n    color: \"pink\"\n  }\n];\n\nconst achievements = [\n  {\n    number: \"2000+\",\n    label: \"Students Graduated\",\n    icon: Users\n  },\n  {\n    number: \"95%\",\n    label: \"Success Rate\",\n    icon: Award\n  },\n  {\n    number: \"15+\",\n    label: \"Years Experience\",\n    icon: Clock\n  },\n  {\n    number: \"50+\",\n    label: \"Expert Teachers\",\n    icon: BookOpen\n  }\n];\n\nconst getColorClasses = (color: string) => {\n  const colors = {\n    blue: \"bg-blue-100 text-blue-600\",\n    green: \"bg-green-100 text-green-600\",\n    purple: \"bg-purple-100 text-purple-600\",\n    orange: \"bg-orange-100 text-orange-600\",\n    red: \"bg-red-100 text-red-600\",\n    indigo: \"bg-indigo-100 text-indigo-600\",\n    teal: \"bg-teal-100 text-teal-600\",\n    pink: \"bg-pink-100 text-pink-600\"\n  };\n  return colors[color as keyof typeof colors] || colors.blue;\n};\n\nexport default function WhyChooseUsSection() {\n  return (\n    <section className=\"py-20 relative overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0\">\n        <img\n          src=\"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=1920&h=1080&fit=crop\"\n          alt=\"Students collaborating\"\n          className=\"w-full h-full object-cover\"\n        />\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/95 to-gray-50/95\"></div>\n      </div>\n\n      <div className=\"relative z-10\">\n        <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Why Choose Us\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              What Makes Us Different\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              We don't just teach subjects; we build futures. Our unique approach combines academic excellence \n              with personal development to ensure holistic growth of every student.\n            </p>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n            {features.map((feature, index) => {\n              const IconComponent = feature.icon;\n              return (\n                <Card key={index} className=\"hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border-0 bg-white/80 backdrop-blur-sm\">\n                  <CardContent className=\"p-6 text-center\">\n                    <div className={`w-16 h-16 rounded-full ${getColorClasses(feature.color)} flex items-center justify-center mx-auto mb-4`}>\n                      <IconComponent className=\"w-8 h-8\" />\n                    </div>\n                    <h3 className=\"text-lg font-bold text-gray-900 mb-2\">\n                      {feature.title}\n                    </h3>\n                    <p className=\"text-gray-600 text-sm mb-3 leading-relaxed\">\n                      {feature.description}\n                    </p>\n                    <Badge variant=\"outline\" className=\"text-xs font-medium\">\n                      {feature.stats}\n                    </Badge>\n                  </CardContent>\n                </Card>\n              );\n            })}\n          </div>\n\n          {/* Achievements Section */}\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg mb-16\">\n            <h3 className=\"text-2xl font-bold text-gray-900 text-center mb-8\">\n              Our Achievements Speak for Themselves\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n              {achievements.map((achievement, index) => {\n                const IconComponent = achievement.icon;\n                return (\n                  <div key={index} className=\"text-center\">\n                    <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <IconComponent className=\"w-8 h-8 text-blue-600\" />\n                    </div>\n                    <div className=\"text-3xl font-bold text-gray-900 mb-2\">\n                      {achievement.number}\n                    </div>\n                    <div className=\"text-gray-600 text-sm\">\n                      {achievement.label}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Testimonial Highlight */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 text-white text-center\">\n            <div className=\"flex justify-center mb-4\">\n              {[...Array(5)].map((_, i) => (\n                <Star key={i} className=\"w-6 h-6 text-yellow-400 fill-current\" />\n              ))}\n            </div>\n            <blockquote className=\"text-xl md:text-2xl font-medium mb-4 italic\">\n              \"Excellence Academy transformed my academic journey. The personalized attention and expert guidance \n              helped me secure admission to my dream engineering college.\"\n            </blockquote>\n            <div className=\"text-blue-200\">\n              <div className=\"font-semibold\">Rajesh Sharma</div>\n              <div className=\"text-sm\">IOE Pulchowk Graduate, 2023</div>\n            </div>\n          </div>\n\n          {/* Call to Action */}\n          <div className=\"text-center mt-16\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Ready to Start Your Success Journey?\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              Join thousands of successful students who have achieved their academic goals with our guidance. \n              Your success story starts here.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button \n                className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Get Enrolled Today\n              </button>\n              <button \n                className=\"border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold px-8 py-3 rounded-lg transition-colors\"\n                onClick={() => {\n                  const aboutSection = document.getElementById('about');\n                  aboutSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Learn More About Us\n              </button>\n            </div>\n          </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAmBA,MAAM,WAAW;IACf;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;QACE,QAAQ;QACR,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,QAAQ;QACR,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,QAAQ;QACR,OAAO;QACP,MAAM,oMAAA,CAAA,QAAK;IACb;IACA;QACE,QAAQ;QACR,OAAO;QACP,MAAM,8MAAA,CAAA,WAAQ;IAChB;CACD;AAED,MAAM,kBAAkB,CAAC;IACvB,MAAM,SAAS;QACb,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,MAAM;QACN,MAAM;IACR;IACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;AAC5D;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACf,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAiC;;;;;;kDAGtE,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAA0D;;;;;;;;;;;;0CAOzE,8OAAC;gCAAI,WAAU;0CACZ,SAAS,GAAG,CAAC,CAAC,SAAS;oCACtB,MAAM,gBAAgB,QAAQ,IAAI;oCAClC,qBACE,8OAAC,gIAAA,CAAA,OAAI;wCAAa,WAAU;kDAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAW,CAAC,uBAAuB,EAAE,gBAAgB,QAAQ,KAAK,EAAE,8CAA8C,CAAC;8DACtH,cAAA,8OAAC;wDAAc,WAAU;;;;;;;;;;;8DAE3B,8OAAC;oDAAG,WAAU;8DACX,QAAQ,KAAK;;;;;;8DAEhB,8OAAC;oDAAE,WAAU;8DACV,QAAQ,WAAW;;;;;;8DAEtB,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,QAAQ,KAAK;;;;;;;;;;;;uCAZT;;;;;gCAiBf;;;;;;0CAIF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAI,WAAU;kDACZ,aAAa,GAAG,CAAC,CAAC,aAAa;4CAC9B,MAAM,gBAAgB,YAAY,IAAI;4CACtC,qBACE,8OAAC;gDAAgB,WAAU;;kEACzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAc,WAAU;;;;;;;;;;;kEAE3B,8OAAC;wDAAI,WAAU;kEACZ,YAAY,MAAM;;;;;;kEAErB,8OAAC;wDAAI,WAAU;kEACZ,YAAY,KAAK;;;;;;;+CARZ;;;;;wCAYd;;;;;;;;;;;;0CAKJ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACZ;+CAAI,MAAM;yCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;gDAAS,WAAU;+CAAb;;;;;;;;;;kDAGf,8OAAC;wCAAW,WAAU;kDAA8C;;;;;;kDAIpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAI,WAAU;0DAAU;;;;;;;;;;;;;;;;;;0CAK7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDAIpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,WAAU;gDACV,SAAS;oDACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;oDAC/C,gBAAgB,eAAe;wDAAE,UAAU;oDAAS;gDACtD;0DACD;;;;;;0DAGD,8OAAC;gDACC,WAAU;gDACV,SAAS;oDACP,MAAM,eAAe,SAAS,cAAc,CAAC;oDAC7C,cAAc,eAAe;wDAAE,UAAU;oDAAS;gDACpD;0DACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUf", "debugId": null}}, {"offset": {"line": 3492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/GallerySection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { \n  Users, \n  Award, \n  BookOpen, \n  Camera,\n  X,\n  ChevronLeft,\n  ChevronRight\n} from \"lucide-react\";\n\n// Mock gallery data - in a real app, these would be actual images\nconst galleryItems = [\n  {\n    id: 1,\n    title: \"SEE Batch 2023 - Classroom Session\",\n    category: \"classroom\",\n    description: \"Interactive mathematics class with our expert faculty\",\n    image: \"https://images.unsplash.com/photo-1509062522246-3755977927d7?w=600&h=400&fit=crop\",\n    alt: \"Students in classroom\"\n  },\n  {\n    id: 2,\n    title: \"Science Laboratory Session\",\n    category: \"activities\",\n    description: \"Hands-on chemistry experiments for +2 Science students\",\n    image: \"https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=600&h=400&fit=crop\",\n    alt: \"Science laboratory\"\n  },\n  {\n    id: 3,\n    title: \"Excellence Award 2023\",\n    category: \"certificates\",\n    description: \"Recognition for outstanding academic performance\",\n    image: \"https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?w=600&h=400&fit=crop\",\n    alt: \"Award ceremony\"\n  },\n  {\n    id: 4,\n    title: \"Group Study Session\",\n    category: \"activities\",\n    description: \"Collaborative learning environment for entrance preparation\",\n    image: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop\",\n    alt: \"Group study\"\n  },\n  {\n    id: 5,\n    title: \"Faculty Training Workshop\",\n    category: \"activities\",\n    description: \"Continuous professional development of our teaching staff\",\n    image: \"https://images.unsplash.com/photo-1524178232363-1fb2b075b655?w=600&h=400&fit=crop\",\n    alt: \"Faculty workshop\"\n  },\n  {\n    id: 6,\n    title: \"Student Achievement Certificate\",\n    category: \"certificates\",\n    description: \"Top performer in Engineering Entrance Examination\",\n    image: \"https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=600&h=400&fit=crop\",\n    alt: \"Achievement certificate\"\n  },\n  {\n    id: 7,\n    title: \"Modern Classroom Facility\",\n    category: \"classroom\",\n    description: \"Well-equipped classrooms with modern teaching aids\",\n    image: \"https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=600&h=400&fit=crop\",\n    alt: \"Modern classroom\"\n  },\n  {\n    id: 8,\n    title: \"Annual Function 2023\",\n    category: \"activities\",\n    description: \"Celebrating academic achievements and cultural activities\",\n    image: \"https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=600&h=400&fit=crop\",\n    alt: \"Annual function\"\n  },\n  {\n    id: 9,\n    title: \"Best Institute Award\",\n    category: \"certificates\",\n    description: \"Recognized as the leading coaching institute in Biratnagar\",\n    image: \"https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=600&h=400&fit=crop\",\n    alt: \"Institute award\"\n  }\n];\n\nconst categories = [\n  { id: \"all\", label: \"All\", icon: Camera },\n  { id: \"classroom\", label: \"Classrooms\", icon: BookOpen },\n  { id: \"activities\", label: \"Activities\", icon: Users },\n  { id: \"certificates\", label: \"Achievements\", icon: Award }\n];\n\nexport default function GallerySection() {\n  const [activeCategory, setActiveCategory] = useState(\"all\");\n  const [selectedImage, setSelectedImage] = useState<typeof galleryItems[0] | null>(null);\n\n  const filteredItems = activeCategory === \"all\" \n    ? galleryItems \n    : galleryItems.filter(item => item.category === activeCategory);\n\n  const openModal = (item: typeof galleryItems[0]) => {\n    setSelectedImage(item);\n  };\n\n  const closeModal = () => {\n    setSelectedImage(null);\n  };\n\n  const navigateImage = (direction: 'prev' | 'next') => {\n    if (!selectedImage) return;\n    \n    const currentIndex = filteredItems.findIndex(item => item.id === selectedImage.id);\n    let newIndex;\n    \n    if (direction === 'prev') {\n      newIndex = currentIndex > 0 ? currentIndex - 1 : filteredItems.length - 1;\n    } else {\n      newIndex = currentIndex < filteredItems.length - 1 ? currentIndex + 1 : 0;\n    }\n    \n    setSelectedImage(filteredItems[newIndex]);\n  };\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Gallery\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Glimpses of Excellence\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Take a look at our vibrant learning environment, student activities, and achievements \n              that showcase the excellence we strive for every day.\n            </p>\n          </div>\n\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n            {categories.map((category) => {\n              const IconComponent = category.icon;\n              return (\n                <Button\n                  key={category.id}\n                  variant={activeCategory === category.id ? \"default\" : \"outline\"}\n                  onClick={() => setActiveCategory(category.id)}\n                  className=\"flex items-center gap-2\"\n                >\n                  <IconComponent className=\"w-4 h-4\" />\n                  {category.label}\n                </Button>\n              );\n            })}\n          </div>\n\n          {/* Gallery Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredItems.map((item) => (\n              <Card \n                key={item.id} \n                className=\"overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group\"\n                onClick={() => openModal(item)}\n              >\n                <div className=\"relative overflow-hidden\">\n                  <img\n                    src={item.image}\n                    alt={item.alt}\n                    className=\"w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center\">\n                    <Camera className=\"w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                  </div>\n                </div>\n                <CardContent className=\"p-4\">\n                  <h3 className=\"font-bold text-gray-900 mb-2\">{item.title}</h3>\n                  <p className=\"text-gray-600 text-sm\">{item.description}</p>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {/* Modal */}\n          {selectedImage && (\n            <div className=\"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4\">\n              <div className=\"relative max-w-4xl max-h-full\">\n                {/* Close Button */}\n                <button\n                  onClick={closeModal}\n                  className=\"absolute top-4 right-4 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all\"\n                >\n                  <X className=\"w-6 h-6\" />\n                </button>\n\n                {/* Navigation Buttons */}\n                <button\n                  onClick={() => navigateImage('prev')}\n                  className=\"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all\"\n                >\n                  <ChevronLeft className=\"w-6 h-6\" />\n                </button>\n                <button\n                  onClick={() => navigateImage('next')}\n                  className=\"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all\"\n                >\n                  <ChevronRight className=\"w-6 h-6\" />\n                </button>\n\n                {/* Image */}\n                <img\n                  src={selectedImage.image}\n                  alt={selectedImage.alt}\n                  className=\"max-w-full max-h-[80vh] object-contain rounded-lg\"\n                />\n\n                {/* Image Info */}\n                <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6 text-white\">\n                  <h3 className=\"text-xl font-bold mb-2\">{selectedImage.title}</h3>\n                  <p className=\"text-gray-300\">{selectedImage.description}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Call to Action */}\n          <div className=\"text-center mt-16\">\n            <div className=\"bg-gray-50 rounded-2xl p-8\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Want to Be Part of Our Success Story?\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Join our community of achievers and create your own success story. \n                Experience the difference quality education makes.\n              </p>\n              <Button \n                size=\"lg\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Start Your Journey Today\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAgBA,kEAAkE;AAClE,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;CACD;AAED,MAAM,aAAa;IACjB;QAAE,IAAI;QAAO,OAAO;QAAO,MAAM,sMAAA,CAAA,SAAM;IAAC;IACxC;QAAE,IAAI;QAAa,OAAO;QAAc,MAAM,8MAAA,CAAA,WAAQ;IAAC;IACvD;QAAE,IAAI;QAAc,OAAO;QAAc,MAAM,oMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,IAAI;QAAgB,OAAO;QAAgB,MAAM,oMAAA,CAAA,QAAK;IAAC;CAC1D;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAElF,MAAM,gBAAgB,mBAAmB,QACrC,eACA,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAElD,MAAM,YAAY,CAAC;QACjB,iBAAiB;IACnB;IAEA,MAAM,aAAa;QACjB,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,eAAe;QAEpB,MAAM,eAAe,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,EAAE;QACjF,IAAI;QAEJ,IAAI,cAAc,QAAQ;YACxB,WAAW,eAAe,IAAI,eAAe,IAAI,cAAc,MAAM,GAAG;QAC1E,OAAO;YACL,WAAW,eAAe,cAAc,MAAM,GAAG,IAAI,eAAe,IAAI;QAC1E;QAEA,iBAAiB,aAAa,CAAC,SAAS;IAC1C;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,gBAAgB,SAAS,IAAI;4BACnC,qBACE,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAS,mBAAmB,SAAS,EAAE,GAAG,YAAY;gCACtD,SAAS,IAAM,kBAAkB,SAAS,EAAE;gCAC5C,WAAU;;kDAEV,8OAAC;wCAAc,WAAU;;;;;;oCACxB,SAAS,KAAK;;+BANV,SAAS,EAAE;;;;;wBAStB;;;;;;kCAIF,8OAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,gIAAA,CAAA,OAAI;gCAEH,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,GAAG;gDACb,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGtB,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAAgC,KAAK,KAAK;;;;;;0DACxD,8OAAC;gDAAE,WAAU;0DAAyB,KAAK,WAAW;;;;;;;;;;;;;+BAhBnD,KAAK,EAAE;;;;;;;;;;oBAuBjB,+BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;8CAIf,8OAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAI1B,8OAAC;oCACC,KAAK,cAAc,KAAK;oCACxB,KAAK,cAAc,GAAG;oCACtB,WAAU;;;;;;8CAIZ,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B,cAAc,KAAK;;;;;;sDAC3D,8OAAC;4CAAE,WAAU;sDAAiB,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;kCAO/D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;wCACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;wCAC/C,gBAAgB,eAAe;4CAAE,UAAU;wCAAS;oCACtD;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 3938, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/TestimonialsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Star, Quote, ChevronLeft, ChevronRight, User } from \"lucide-react\";\n\nconst testimonials = [\n  {\n    id: 1,\n    name: \"<PERSON><PERSON>\",\n    role: \"SEE Graduate 2023\",\n    course: \"SEE Preparation\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"Excellence Academy completely transformed my approach to studying. The teachers here don't just teach subjects; they build confidence. I scored 3.95 GPA in SEE, which seemed impossible before joining here. The individual attention and regular tests helped me identify my weak areas and improve them systematically.\",\n    achievement: \"3.95 GPA in SEE\"\n  },\n  {\n    id: 2,\n    name: \"<PERSON><PERSON>\",\n    role: \"Engineering Student\",\n    course: \"Engineering Entrance\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"The entrance preparation course at Excellence Academy is simply outstanding. The faculty's deep understanding of exam patterns and their teaching methodology helped me crack IOE entrance exam. The mock tests were exactly like the real exam, which boosted my confidence tremendously.\",\n    achievement: \"IOE Pulchowk Admission\"\n  },\n  {\n    id: 3,\n    name: \"Sunita Rai\",\n    role: \"Parent\",\n    course: \"+2 Science\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-*************-6461ffad8d80?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"As a parent, I was initially worried about my daughter's performance in +2 Science. But Excellence Academy's supportive environment and excellent teaching staff helped her excel. The regular parent-teacher meetings kept me informed about her progress. She's now pursuing MBBS!\",\n    achievement: \"Daughter got MBBS admission\"\n  },\n  {\n    id: 4,\n    name: \"Amit Kumar\",\n    role: \"+2 Management Graduate\",\n    course: \"+2 Management\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"The practical approach to teaching business studies and economics at Excellence Academy is remarkable. The teachers use real-world examples that made complex concepts easy to understand. I secured first division and got admission to my preferred college for BBS.\",\n    achievement: \"First Division in +2\"\n  },\n  {\n    id: 5,\n    name: \"Anita Gurung\",\n    role: \"Medical Student\",\n    course: \"Medical Entrance\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-**********-94ddf0286df2?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"The medical entrance preparation at Excellence Academy is top-notch. The biology faculty's expertise and the comprehensive study materials provided gave me the edge I needed. The regular practice sessions and doubt-clearing classes were incredibly helpful in my MBBS entrance preparation.\",\n    achievement: \"MBBS Admission Secured\"\n  },\n  {\n    id: 6,\n    name: \"Krishna Bahadur\",\n    role: \"Parent\",\n    course: \"Foundation Course\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-**********-0b93528c311a?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"My son was struggling with mathematics in class 9. Excellence Academy's foundation course not only improved his grades but also built his confidence. The teachers are patient and understanding. Now he's one of the top students in his class. Highly recommended!\",\n    achievement: \"Son improved from C to A grade\"\n  }\n];\n\nexport default function TestimonialsSection() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying) return;\n    \n    const interval = setInterval(() => {\n      setCurrentIndex((prevIndex) => \n        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1\n      );\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying]);\n\n  const goToNext = () => {\n    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);\n  };\n\n  const goToPrev = () => {\n    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);\n  };\n\n  const goToSlide = (index: number) => {\n    setCurrentIndex(index);\n  };\n\n  const currentTestimonial = testimonials[currentIndex];\n\n  return (\n    <section className=\"py-20 relative overflow-hidden\">\n      {/* Background Image */}\n      <div className=\"absolute inset-0\">\n        <img\n          src=\"https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=1920&h=1080&fit=crop\"\n          alt=\"Happy students\"\n          className=\"w-full h-full object-cover\"\n        />\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/95 to-indigo-100/95\"></div>\n      </div>\n\n      <div className=\"relative z-10\">\n        <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Testimonials\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              What Our Students Say\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Don't just take our word for it. Hear from our successful students and parents \n              who have experienced the Excellence Academy difference firsthand.\n            </p>\n          </div>\n\n          {/* Main Testimonial Card */}\n          <div className=\"relative mb-12\">\n            <Card \n              className=\"max-w-4xl mx-auto bg-white shadow-2xl border-0 overflow-hidden\"\n              onMouseEnter={() => setIsAutoPlaying(false)}\n              onMouseLeave={() => setIsAutoPlaying(true)}\n            >\n              <CardContent className=\"p-8 md:p-12\">\n                <div className=\"flex flex-col md:flex-row items-center gap-8\">\n                  {/* Profile Image */}\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"relative\">\n                      <img\n                        src={currentTestimonial.image}\n                        alt={currentTestimonial.name}\n                        className=\"w-24 h-24 md:w-32 md:h-32 rounded-full object-cover border-4 border-blue-200\"\n                      />\n                      <div className=\"absolute -top-2 -right-2 bg-blue-600 text-white p-2 rounded-full\">\n                        <Quote className=\"w-4 h-4\" />\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Testimonial Content */}\n                  <div className=\"flex-1 text-center md:text-left\">\n                    {/* Rating */}\n                    <div className=\"flex justify-center md:justify-start mb-4\">\n                      {[...Array(currentTestimonial.rating)].map((_, i) => (\n                        <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                      ))}\n                    </div>\n\n                    {/* Testimonial Text */}\n                    <blockquote className=\"text-lg md:text-xl text-gray-700 leading-relaxed mb-6 italic\">\n                      \"{currentTestimonial.testimonial}\"\n                    </blockquote>\n\n                    {/* Author Info */}\n                    <div className=\"space-y-2\">\n                      <div className=\"font-bold text-xl text-gray-900\">\n                        {currentTestimonial.name}\n                      </div>\n                      <div className=\"text-gray-600\">\n                        {currentTestimonial.role}\n                      </div>\n                      <div className=\"flex flex-col sm:flex-row gap-2 justify-center md:justify-start\">\n                        <Badge variant=\"outline\" className=\"text-blue-600 border-blue-200\">\n                          {currentTestimonial.course}\n                        </Badge>\n                        <Badge className=\"bg-green-100 text-green-700 border-green-200\">\n                          {currentTestimonial.achievement}\n                        </Badge>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Navigation Buttons */}\n            <button\n              onClick={goToPrev}\n              className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 p-3 rounded-full transition-all duration-200 hover:scale-110\"\n            >\n              <ChevronLeft className=\"w-6 h-6\" />\n            </button>\n            <button\n              onClick={goToNext}\n              className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 p-3 rounded-full transition-all duration-200 hover:scale-110\"\n            >\n              <ChevronRight className=\"w-6 h-6\" />\n            </button>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center space-x-2 mb-12\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                  index === currentIndex \n                    ? 'bg-blue-600 w-8' \n                    : 'bg-gray-300 hover:bg-gray-400'\n                }`}\n              />\n            ))}\n          </div>\n\n          {/* All Testimonials Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {testimonials.map((testimonial, index) => (\n              <Card \n                key={testimonial.id} \n                className={`hover:shadow-lg transition-all duration-300 cursor-pointer ${\n                  index === currentIndex ? 'ring-2 ring-blue-400 shadow-lg' : ''\n                }`}\n                onClick={() => goToSlide(index)}\n              >\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center mb-4\">\n                    <img\n                      src={testimonial.image}\n                      alt={testimonial.name}\n                      className=\"w-12 h-12 rounded-full object-cover mr-4\"\n                    />\n                    <div>\n                      <div className=\"font-semibold text-gray-900\">{testimonial.name}</div>\n                      <div className=\"text-sm text-gray-600\">{testimonial.role}</div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex mb-3\">\n                    {[...Array(testimonial.rating)].map((_, i) => (\n                      <Star key={i} className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                    ))}\n                  </div>\n                  \n                  <p className=\"text-gray-600 text-sm leading-relaxed line-clamp-3\">\n                    {testimonial.testimonial.substring(0, 120)}...\n                  </p>\n                  \n                  <Badge variant=\"outline\" className=\"mt-3 text-xs\">\n                    {testimonial.achievement}\n                  </Badge>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {/* Call to Action */}\n          <div className=\"text-center mt-16\">\n            <div className=\"bg-white rounded-2xl p-8 shadow-lg\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Ready to Write Your Success Story?\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Join our community of successful students and experience the transformation yourself. \n                Your journey to academic excellence starts here.\n              </p>\n              <Button \n                size=\"lg\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Start Your Journey Today\n              </Button>\n            </div>\n          </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe;QAEpB,MAAM,WAAW,YAAY;YAC3B,gBAAgB,CAAC,YACf,cAAc,aAAa,MAAM,GAAG,IAAI,IAAI,YAAY;QAE5D,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAc;IAElB,MAAM,WAAW;QACf,gBAAgB,iBAAiB,aAAa,MAAM,GAAG,IAAI,IAAI,eAAe;IAChF;IAEA,MAAM,WAAW;QACf,gBAAgB,iBAAiB,IAAI,aAAa,MAAM,GAAG,IAAI,eAAe;IAChF;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAI;wBACJ,KAAI;wBACJ,WAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACf,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAAiC;;;;;;kDAGtE,8OAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,8OAAC;wCAAE,WAAU;kDAA0D;;;;;;;;;;;;0CAOzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,OAAI;wCACH,WAAU;wCACV,cAAc,IAAM,iBAAiB;wCACrC,cAAc,IAAM,iBAAiB;kDAErC,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEACC,KAAK,mBAAmB,KAAK;oEAC7B,KAAK,mBAAmB,IAAI;oEAC5B,WAAU;;;;;;8EAEZ,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kEAMvB,8OAAC;wDAAI,WAAU;;0EAEb,8OAAC;gEAAI,WAAU;0EACZ;uEAAI,MAAM,mBAAmB,MAAM;iEAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC7C,8OAAC,kMAAA,CAAA,OAAI;wEAAS,WAAU;uEAAb;;;;;;;;;;0EAKf,8OAAC;gEAAW,WAAU;;oEAA+D;oEACjF,mBAAmB,WAAW;oEAAC;;;;;;;0EAInC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,mBAAmB,IAAI;;;;;;kFAE1B,8OAAC;wEAAI,WAAU;kFACZ,mBAAmB,IAAI;;;;;;kFAE1B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAChC,mBAAmB,MAAM;;;;;;0FAE5B,8OAAC,iIAAA,CAAA,QAAK;gFAAC,WAAU;0FACd,mBAAmB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAU7C,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC;wCACC,SAAS;wCACT,WAAU;kDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAK5B,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,8OAAC;wCAEC,SAAS,IAAM,UAAU;wCACzB,WAAW,CAAC,iDAAiD,EAC3D,UAAU,eACN,oBACA,iCACJ;uCANG;;;;;;;;;;0CAYX,8OAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC,gIAAA,CAAA,OAAI;wCAEH,WAAW,CAAC,2DAA2D,EACrE,UAAU,eAAe,mCAAmC,IAC5D;wCACF,SAAS,IAAM,UAAU;kDAEzB,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DACC,KAAK,YAAY,KAAK;4DACtB,KAAK,YAAY,IAAI;4DACrB,WAAU;;;;;;sEAEZ,8OAAC;;8EACC,8OAAC;oEAAI,WAAU;8EAA+B,YAAY,IAAI;;;;;;8EAC9D,8OAAC;oEAAI,WAAU;8EAAyB,YAAY,IAAI;;;;;;;;;;;;;;;;;;8DAI5D,8OAAC;oDAAI,WAAU;8DACZ;2DAAI,MAAM,YAAY,MAAM;qDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,8OAAC,kMAAA,CAAA,OAAI;4DAAS,WAAU;2DAAb;;;;;;;;;;8DAIf,8OAAC;oDAAE,WAAU;;wDACV,YAAY,WAAW,CAAC,SAAS,CAAC,GAAG;wDAAK;;;;;;;8DAG7C,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,YAAY,WAAW;;;;;;;;;;;;uCA9BvB,YAAY,EAAE;;;;;;;;;;0CAsCzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDAGtD,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAIlC,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS;gDACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;gDAC/C,gBAAgB,eAAe;oDAAE,UAAU;gDAAS;4CACtD;sDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUf", "debugId": null}}, {"offset": {"line": 4492, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4516, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4539, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/ContactSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { \n  Phone, \n  Mail, \n  MapPin, \n  Clock, \n  Send,\n  CheckCircle,\n  User,\n  MessageSquare,\n  BookOpen\n} from \"lucide-react\";\n\nconst courses = [\n  \"SEE Preparation\",\n  \"+2 Science\",\n  \"+2 Management\", \n  \"Engineering Entrance\",\n  \"Medical Entrance\",\n  \"Foundation Course (Class 8-10)\",\n  \"Other\"\n];\n\nconst contactInfo = [\n  {\n    icon: Phone,\n    title: \"Phone Numbers\",\n    details: [\"+977-21-123456\", \"+977-**********\"],\n    color: \"green\"\n  },\n  {\n    icon: Mail,\n    title: \"Email Address\",\n    details: [\"<EMAIL>\", \"<EMAIL>\"],\n    color: \"blue\"\n  },\n  {\n    icon: MapPin,\n    title: \"Address\",\n    details: [\"Main Road, Biratnagar-10\", \"Morang, Province 1, Nepal\"],\n    color: \"red\"\n  },\n  {\n    icon: Clock,\n    title: \"Office Hours\",\n    details: [\"Mon-Fri: 6:00 AM - 8:00 PM\", \"Sat-Sun: 6:00 AM - 6:00 PM\"],\n    color: \"purple\"\n  }\n];\n\nexport default function ContactSection() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    phone: \"\",\n    email: \"\",\n    course: \"\",\n    message: \"\"\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    setIsSubmitting(false);\n    setIsSubmitted(true);\n    \n    // Reset form after 3 seconds\n    setTimeout(() => {\n      setIsSubmitted(false);\n      setFormData({\n        name: \"\",\n        phone: \"\",\n        email: \"\",\n        course: \"\",\n        message: \"\"\n      });\n    }, 3000);\n  };\n\n  const getColorClasses = (color: string) => {\n    const colors = {\n      green: \"bg-green-100 text-green-600\",\n      blue: \"bg-blue-100 text-blue-600\",\n      red: \"bg-red-100 text-red-600\",\n      purple: \"bg-purple-100 text-purple-600\"\n    };\n    return colors[color as keyof typeof colors] || colors.blue;\n  };\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Contact Us\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Get in Touch\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Ready to start your academic journey with us? We're here to help you choose the right course \n              and answer all your questions. Contact us today!\n            </p>\n          </div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <Card className=\"shadow-lg border-0\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-bold text-gray-900 flex items-center\">\n                  <MessageSquare className=\"w-6 h-6 mr-3 text-blue-600\" />\n                  Send us a Message\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                {isSubmitted ? (\n                  <div className=\"text-center py-8\">\n                    <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <CheckCircle className=\"w-8 h-8 text-green-600\" />\n                    </div>\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Message Sent Successfully!</h3>\n                    <p className=\"text-gray-600\">\n                      Thank you for your interest. We'll get back to you within 24 hours.\n                    </p>\n                  </div>\n                ) : (\n                  <form onSubmit={handleSubmit} className=\"space-y-6\">\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      <div>\n                        <Label htmlFor=\"name\" className=\"flex items-center mb-2\">\n                          <User className=\"w-4 h-4 mr-2\" />\n                          Full Name *\n                        </Label>\n                        <Input\n                          id=\"name\"\n                          name=\"name\"\n                          type=\"text\"\n                          required\n                          value={formData.name}\n                          onChange={handleInputChange}\n                          placeholder=\"Enter your full name\"\n                          className=\"h-12\"\n                        />\n                      </div>\n                      <div>\n                        <Label htmlFor=\"phone\" className=\"flex items-center mb-2\">\n                          <Phone className=\"w-4 h-4 mr-2\" />\n                          Phone Number *\n                        </Label>\n                        <Input\n                          id=\"phone\"\n                          name=\"phone\"\n                          type=\"tel\"\n                          required\n                          value={formData.phone}\n                          onChange={handleInputChange}\n                          placeholder=\"Enter your phone number\"\n                          className=\"h-12\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"email\" className=\"flex items-center mb-2\">\n                        <Mail className=\"w-4 h-4 mr-2\" />\n                        Email Address\n                      </Label>\n                      <Input\n                        id=\"email\"\n                        name=\"email\"\n                        type=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        placeholder=\"Enter your email address\"\n                        className=\"h-12\"\n                      />\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"course\" className=\"flex items-center mb-2\">\n                        <BookOpen className=\"w-4 h-4 mr-2\" />\n                        Course of Interest *\n                      </Label>\n                      <select\n                        id=\"course\"\n                        name=\"course\"\n                        required\n                        value={formData.course}\n                        onChange={handleInputChange}\n                        className=\"w-full h-12 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      >\n                        <option value=\"\">Select a course</option>\n                        {courses.map((course, index) => (\n                          <option key={index} value={course}>\n                            {course}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"message\" className=\"flex items-center mb-2\">\n                        <MessageSquare className=\"w-4 h-4 mr-2\" />\n                        Message\n                      </Label>\n                      <Textarea\n                        id=\"message\"\n                        name=\"message\"\n                        value={formData.message}\n                        onChange={handleInputChange}\n                        placeholder=\"Tell us about your academic goals, questions, or any specific requirements...\"\n                        className=\"min-h-[120px] resize-none\"\n                      />\n                    </div>\n\n                    <Button \n                      type=\"submit\" \n                      className=\"w-full h-12 text-lg font-semibold\"\n                      disabled={isSubmitting}\n                    >\n                      {isSubmitting ? (\n                        <>\n                          <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                          Sending...\n                        </>\n                      ) : (\n                        <>\n                          <Send className=\"w-4 h-4 mr-2\" />\n                          Send Message\n                        </>\n                      )}\n                    </Button>\n                  </form>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Contact Information */}\n            <div className=\"space-y-6\">\n              {/* Contact Image */}\n              <div className=\"relative rounded-2xl overflow-hidden shadow-lg\">\n                <img\n                  src=\"https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=300&fit=crop\"\n                  alt=\"Contact us - Students and teachers\"\n                  className=\"w-full h-48 object-cover\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent\"></div>\n                <div className=\"absolute bottom-4 left-4 text-white\">\n                  <h3 className=\"text-lg font-bold\">Ready to Start Your Journey?</h3>\n                  <p className=\"text-sm text-gray-200\">Contact us today for admission guidance</p>\n                </div>\n              </div>\n              <Card className=\"shadow-lg border-0\">\n                <CardHeader>\n                  <CardTitle className=\"text-2xl font-bold text-gray-900\">\n                    Contact Information\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  {contactInfo.map((info, index) => {\n                    const IconComponent = info.icon;\n                    return (\n                      <div key={index} className=\"flex items-start space-x-4\">\n                        <div className={`w-12 h-12 rounded-full ${getColorClasses(info.color)} flex items-center justify-center flex-shrink-0`}>\n                          <IconComponent className=\"w-5 h-5\" />\n                        </div>\n                        <div>\n                          <h3 className=\"font-semibold text-gray-900 mb-1\">{info.title}</h3>\n                          {info.details.map((detail, detailIndex) => (\n                            <p key={detailIndex} className=\"text-gray-600 text-sm\">\n                              {detail}\n                            </p>\n                          ))}\n                        </div>\n                      </div>\n                    );\n                  })}\n                </CardContent>\n              </Card>\n\n              {/* Quick Info */}\n              <Card className=\"shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                    Why Contact Us?\n                  </h3>\n                  <ul className=\"space-y-3\">\n                    <li className=\"flex items-center text-gray-700\">\n                      <CheckCircle className=\"w-4 h-4 text-green-500 mr-3 flex-shrink-0\" />\n                      Free consultation and course guidance\n                    </li>\n                    <li className=\"flex items-center text-gray-700\">\n                      <CheckCircle className=\"w-4 h-4 text-green-500 mr-3 flex-shrink-0\" />\n                      Flexible batch timings available\n                    </li>\n                    <li className=\"flex items-center text-gray-700\">\n                      <CheckCircle className=\"w-4 h-4 text-green-500 mr-3 flex-shrink-0\" />\n                      Special discounts for early enrollment\n                    </li>\n                    <li className=\"flex items-center text-gray-700\">\n                      <CheckCircle className=\"w-4 h-4 text-green-500 mr-3 flex-shrink-0\" />\n                      Demo classes available\n                    </li>\n                  </ul>\n                </CardContent>\n              </Card>\n\n              {/* Emergency Contact */}\n              <Card className=\"shadow-lg border-0 bg-gradient-to-br from-red-50 to-pink-50\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                    Emergency Contact\n                  </h3>\n                  <p className=\"text-gray-600 mb-3\">\n                    For urgent inquiries or admission-related emergencies:\n                  </p>\n                  <div className=\"flex items-center text-red-600 font-semibold\">\n                    <Phone className=\"w-4 h-4 mr-2\" />\n                    +977-********** (24/7)\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAqBA,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,cAAc;IAClB;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAAkB;SAAkB;QAC9C,OAAO;IACT;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,SAAS;YAAC;YAA8B;SAAkC;QAC1E,OAAO;IACT;IACA;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,SAAS;YAAC;YAA4B;SAA4B;QAClE,OAAO;IACT;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAA8B;SAA6B;QACrE,OAAO;IACT;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,eAAe;QAEf,6BAA6B;QAC7B,WAAW;YACT,eAAe;YACf,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,QAAQ;gBACR,SAAS;YACX;QACF,GAAG;IACL;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS;YACb,OAAO;YACP,MAAM;YACN,KAAK;YACL,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;IAC5D;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;;;;;;kDAI5D,8OAAC,gIAAA,CAAA,cAAW;kDACT,4BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;iEAK/B,8OAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAO,WAAU;;sFAC9B,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGnC,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,QAAQ;oEACR,OAAO,SAAS,IAAI;oEACpB,UAAU;oEACV,aAAY;oEACZ,WAAU;;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAQ,WAAU;;sFAC/B,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGpC,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,QAAQ;oEACR,OAAO,SAAS,KAAK;oEACrB,UAAU;oEACV,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;;8EAC/B,8OAAC,kMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAId,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAS,WAAU;;8EAChC,8OAAC,8MAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,8OAAC;4DACC,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,MAAM;4DACtB,UAAU;4DACV,WAAU;;8EAEV,8OAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;wEAAmB,OAAO;kFACxB;uEADU;;;;;;;;;;;;;;;;;8DAOnB,8OAAC;;sEACC,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;8EACjC,8OAAC,wNAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAG5C,8OAAC,oIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAId,8OAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;oDACV,UAAU;8DAET,6BACC;;0EACE,8OAAC;gEAAI,WAAU;;;;;;4DAAuE;;qFAIxF;;0EACE,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0CAW/C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoB;;;;;;kEAClC,8OAAC;wDAAE,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;kDAGzC,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;0DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAmC;;;;;;;;;;;0DAI1D,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACpB,YAAY,GAAG,CAAC,CAAC,MAAM;oDACtB,MAAM,gBAAgB,KAAK,IAAI;oDAC/B,qBACE,8OAAC;wDAAgB,WAAU;;0EACzB,8OAAC;gEAAI,WAAW,CAAC,uBAAuB,EAAE,gBAAgB,KAAK,KAAK,EAAE,+CAA+C,CAAC;0EACpH,cAAA,8OAAC;oEAAc,WAAU;;;;;;;;;;;0EAE3B,8OAAC;;kFACC,8OAAC;wEAAG,WAAU;kFAAoC,KAAK,KAAK;;;;;;oEAC3D,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,8OAAC;4EAAoB,WAAU;sFAC5B;2EADK;;;;;;;;;;;;uDAPJ;;;;;gDAcd;;;;;;;;;;;;kDAKJ,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAGrD,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC,2NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;;;;;;;;;;;;;;;;;;kDAQ7E,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAGrD,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtD", "debugId": null}}, {"offset": {"line": 5387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/LocationSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { \n  MapPin, \n  Navigation, \n  Bus, \n  Car, \n  Clock,\n  Phone,\n  ExternalLink\n} from \"lucide-react\";\n\nconst transportOptions = [\n  {\n    icon: Bus,\n    title: \"Public Transport\",\n    description: \"Regular bus services from all major areas of Biratnagar\",\n    routes: [\"Route 1: From Traffic Chowk\", \"Route 2: From Rani Chowk\", \"Route 3: From Hospital Area\"]\n  },\n  {\n    icon: Car,\n    title: \"Private Vehicle\",\n    description: \"Ample parking space available for cars and motorcycles\",\n    routes: [\"Free parking for students\", \"Secure parking area\", \"Easy access from main road\"]\n  },\n  {\n    icon: Navigation,\n    title: \"Walking Distance\",\n    description: \"Easily accessible on foot from nearby residential areas\",\n    routes: [\"5 min from Main Chowk\", \"10 min from Bus Park\", \"3 min from Bank Area\"]\n  }\n];\n\nconst nearbyLandmarks = [\n  { name: \"Biratnagar Hospital\", distance: \"500m\", direction: \"North\" },\n  { name: \"Traffic Police Office\", distance: \"300m\", direction: \"South\" },\n  { name: \"Nepal Bank\", distance: \"200m\", direction: \"East\" },\n  { name: \"Main Bus Park\", distance: \"800m\", direction: \"West\" },\n  { name: \"District Court\", distance: \"600m\", direction: \"Northeast\" },\n  { name: \"Shopping Complex\", distance: \"400m\", direction: \"Southwest\" }\n];\n\nexport default function LocationSection() {\n  const openGoogleMaps = () => {\n    // Coordinates for Biratnagar (approximate location)\n    const lat = 26.4525;\n    const lng = 87.2718;\n    const url = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}&query_place_id=Excellence+Academy+Biratnagar`;\n    window.open(url, '_blank');\n  };\n\n  const getDirections = () => {\n    const lat = 26.4525;\n    const lng = 87.2718;\n    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&destination_place_id=Excellence+Academy+Biratnagar`;\n    window.open(url, '_blank');\n  };\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Our Location\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Find Us in Biratnagar\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Conveniently located in the heart of Biratnagar, our institute is easily accessible \n              from all parts of the city. Visit us today to explore our facilities.\n            </p>\n          </div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Map Section */}\n            <div className=\"space-y-6\">\n              <Card className=\"overflow-hidden shadow-lg border-0\">\n                <div className=\"relative\">\n                  {/* Google Maps Embed */}\n                  <div className=\"aspect-video bg-gray-200 relative\">\n                    <iframe\n                      src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3570.8234567890123!2d87.2718!3d26.4525!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzA5LjAiTiA4N8KwMTYnMTguNSJF!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp\"\n                      width=\"100%\"\n                      height=\"100%\"\n                      style={{ border: 0 }}\n                      allowFullScreen\n                      loading=\"lazy\"\n                      referrerPolicy=\"no-referrer-when-downgrade\"\n                      title=\"Excellence Academy Location\"\n                      className=\"absolute inset-0\"\n                    ></iframe>\n                  </div>\n                  \n                  {/* Map Overlay */}\n                  <div className=\"absolute top-4 left-4 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-lg\">\n                    <div className=\"flex items-center text-gray-900\">\n                      <MapPin className=\"w-5 h-5 text-red-500 mr-2\" />\n                      <div>\n                        <div className=\"font-semibold text-sm\">Excellence Academy</div>\n                        <div className=\"text-xs text-gray-600\">Main Road, Biratnagar</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <CardContent className=\"p-6\">\n                  <div className=\"flex flex-col sm:flex-row gap-3\">\n                    <Button \n                      onClick={openGoogleMaps}\n                      className=\"flex-1 flex items-center justify-center\"\n                    >\n                      <ExternalLink className=\"w-4 h-4 mr-2\" />\n                      View on Google Maps\n                    </Button>\n                    <Button \n                      variant=\"outline\"\n                      onClick={getDirections}\n                      className=\"flex-1 flex items-center justify-center\"\n                    >\n                      <Navigation className=\"w-4 h-4 mr-2\" />\n                      Get Directions\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Address Card */}\n              <Card className=\"shadow-lg border-0\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\n                    <MapPin className=\"w-5 h-5 text-red-500 mr-3\" />\n                    Complete Address\n                  </h3>\n                  <div className=\"space-y-2 text-gray-600\">\n                    <p className=\"font-semibold\">Excellence Academy</p>\n                    <p>Main Road, Ward No. 10</p>\n                    <p>Biratnagar, Morang</p>\n                    <p>Province 1, Nepal</p>\n                    <p className=\"flex items-center mt-3\">\n                      <Phone className=\"w-4 h-4 mr-2\" />\n                      +977-21-123456\n                    </p>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Location Information */}\n            <div className=\"space-y-6\">\n              {/* Transport Options */}\n              <Card className=\"shadow-lg border-0\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-6\">\n                    How to Reach Us\n                  </h3>\n                  <div className=\"space-y-6\">\n                    {transportOptions.map((option, index) => {\n                      const IconComponent = option.icon;\n                      return (\n                        <div key={index} className=\"flex items-start space-x-4\">\n                          <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                            <IconComponent className=\"w-6 h-6 text-blue-600\" />\n                          </div>\n                          <div className=\"flex-1\">\n                            <h4 className=\"font-semibold text-gray-900 mb-2\">{option.title}</h4>\n                            <p className=\"text-gray-600 text-sm mb-3\">{option.description}</p>\n                            <ul className=\"space-y-1\">\n                              {option.routes.map((route, routeIndex) => (\n                                <li key={routeIndex} className=\"text-xs text-gray-500 flex items-center\">\n                                  <div className=\"w-1 h-1 bg-blue-400 rounded-full mr-2\"></div>\n                                  {route}\n                                </li>\n                              ))}\n                            </ul>\n                          </div>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Nearby Landmarks */}\n              <Card className=\"shadow-lg border-0\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-6\">\n                    Nearby Landmarks\n                  </h3>\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                    {nearbyLandmarks.map((landmark, index) => (\n                      <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                        <div>\n                          <div className=\"font-medium text-gray-900 text-sm\">{landmark.name}</div>\n                          <div className=\"text-xs text-gray-500\">{landmark.direction}</div>\n                        </div>\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {landmark.distance}\n                        </Badge>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Visit Information */}\n              <Card className=\"shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\n                    <Clock className=\"w-5 h-5 text-blue-600 mr-3\" />\n                    Visit Us\n                  </h3>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Office Hours</h4>\n                      <div className=\"space-y-1 text-sm text-gray-600\">\n                        <p>Monday - Friday: 6:00 AM - 8:00 PM</p>\n                        <p>Saturday - Sunday: 6:00 AM - 6:00 PM</p>\n                      </div>\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Best Time to Visit</h4>\n                      <p className=\"text-sm text-gray-600\">\n                        For admissions and inquiries, visit us between 10:00 AM - 5:00 PM \n                        when our counselors are available.\n                      </p>\n                    </div>\n                    <div className=\"pt-4 border-t border-blue-200\">\n                      <Button \n                        className=\"w-full\"\n                        onClick={() => {\n                          const contactSection = document.getElementById('contact');\n                          contactSection?.scrollIntoView({ behavior: 'smooth' });\n                        }}\n                      >\n                        Schedule a Visit\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n\n          {/* Additional Information */}\n          <div className=\"mt-16 text-center\">\n            <Card className=\"shadow-lg border-0 bg-white\">\n              <CardContent className=\"p-8\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  Can't Find Us?\n                </h3>\n                <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n                  If you're having trouble locating our institute, don't hesitate to call us. \n                  Our staff will be happy to guide you with detailed directions.\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                  <Button \n                    size=\"lg\"\n                    onClick={() => window.open('tel:+977-21-123456', '_self')}\n                  >\n                    <Phone className=\"w-4 h-4 mr-2\" />\n                    Call for Directions\n                  </Button>\n                  <Button \n                    size=\"lg\"\n                    variant=\"outline\"\n                    onClick={getDirections}\n                  >\n                    <Navigation className=\"w-4 h-4 mr-2\" />\n                    GPS Navigation\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAeA,MAAM,mBAAmB;IACvB;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;YAA+B;YAA4B;SAA8B;IACpG;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;YAA6B;YAAuB;SAA6B;IAC5F;IACA;QACE,MAAM,8MAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;YAAyB;YAAwB;SAAuB;IACnF;CACD;AAED,MAAM,kBAAkB;IACtB;QAAE,MAAM;QAAuB,UAAU;QAAQ,WAAW;IAAQ;IACpE;QAAE,MAAM;QAAyB,UAAU;QAAQ,WAAW;IAAQ;IACtE;QAAE,MAAM;QAAc,UAAU;QAAQ,WAAW;IAAO;IAC1D;QAAE,MAAM;QAAiB,UAAU;QAAQ,WAAW;IAAO;IAC7D;QAAE,MAAM;QAAkB,UAAU;QAAQ,WAAW;IAAY;IACnE;QAAE,MAAM;QAAoB,UAAU;QAAQ,WAAW;IAAY;CACtE;AAEc,SAAS;IACtB,MAAM,iBAAiB;QACrB,oDAAoD;QACpD,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM,CAAC,gDAAgD,EAAE,IAAI,CAAC,EAAE,IAAI,6CAA6C,CAAC;QACxH,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,MAAM,gBAAgB;QACpB,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM,CAAC,mDAAmD,EAAE,IAAI,CAAC,EAAE,IAAI,mDAAmD,CAAC;QACjI,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAI;4DACJ,OAAM;4DACN,QAAO;4DACP,OAAO;gEAAE,QAAQ;4DAAE;4DACnB,eAAe;4DACf,SAAQ;4DACR,gBAAe;4DACf,OAAM;4DACN,WAAU;;;;;;;;;;;kEAKd,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;sFACvC,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM/C,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,WAAU;;8EAEV,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAG3C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS;4DACT,WAAU;;8EAEV,8OAAC,8MAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/C,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAA8B;;;;;;;8DAGlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;sEAAE;;;;;;sEACH,8OAAC;sEAAE;;;;;;sEACH,8OAAC;sEAAE;;;;;;sEACH,8OAAC;4DAAE,WAAU;;8EACX,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS5C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAGrD,8OAAC;oDAAI,WAAU;8DACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ;wDAC7B,MAAM,gBAAgB,OAAO,IAAI;wDACjC,qBACE,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAc,WAAU;;;;;;;;;;;8EAE3B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAoC,OAAO,KAAK;;;;;;sFAC9D,8OAAC;4EAAE,WAAU;sFAA8B,OAAO,WAAW;;;;;;sFAC7D,8OAAC;4EAAG,WAAU;sFACX,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BACzB,8OAAC;oFAAoB,WAAU;;sGAC7B,8OAAC;4FAAI,WAAU;;;;;;wFACd;;mFAFM;;;;;;;;;;;;;;;;;2DATP;;;;;oDAkBd;;;;;;;;;;;;;;;;;kDAMN,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAGrD,8OAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,sBAC9B,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAqC,SAAS,IAAI;;;;;;sFACjE,8OAAC;4EAAI,WAAU;sFAAyB,SAAS,SAAS;;;;;;;;;;;;8EAE5D,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAChC,SAAS,QAAQ;;;;;;;2DANZ;;;;;;;;;;;;;;;;;;;;;kDAelB,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAA+B;;;;;;;8DAGlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAE;;;;;;sFACH,8OAAC;sFAAE;;;;;;;;;;;;;;;;;;sEAGP,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,WAAU;gEACV,SAAS;oEACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;oEAC/C,gBAAgB,eAAe;wEAAE,UAAU;oEAAS;gEACtD;0EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDAIpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,sBAAsB;;kEAEjD,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;;kEAET,8OAAC,8MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D", "debugId": null}}, {"offset": {"line": 6194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD;IACrD,qBACE,8OAAC,qKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 6222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/Footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { \n  GraduationCap,\n  Phone, \n  Mail, \n  MapPin, \n  Clock,\n  Facebook,\n  Instagram,\n  Youtube,\n  Twitter,\n  ExternalLink,\n  Heart\n} from \"lucide-react\";\n\nconst quickLinks = [\n  { name: \"About Us\", href: \"#about\" },\n  { name: \"Courses\", href: \"#courses\" },\n  { name: \"Our Faculty\", href: \"#faculty\" },\n  { name: \"Why Choose Us\", href: \"#why-choose-us\" },\n  { name: \"Gallery\", href: \"#gallery\" },\n  { name: \"Testimonials\", href: \"#testimonials\" },\n  { name: \"Contact\", href: \"#contact\" }\n];\n\nconst courses = [\n  { name: \"SEE Preparation\", href: \"#contact\" },\n  { name: \"+2 Science\", href: \"#contact\" },\n  { name: \"+2 Management\", href: \"#contact\" },\n  { name: \"Engineering Entrance\", href: \"#contact\" },\n  { name: \"Medical Entrance\", href: \"#contact\" },\n  { name: \"Foundation Course\", href: \"#contact\" }\n];\n\nconst socialLinks = [\n  { \n    name: \"Facebook\", \n    icon: Facebook, \n    href: \"https://facebook.com/excellenceacademy\",\n    color: \"hover:text-blue-600\"\n  },\n  { \n    name: \"Instagram\", \n    icon: Instagram, \n    href: \"https://instagram.com/excellenceacademy\",\n    color: \"hover:text-pink-600\"\n  },\n  { \n    name: \"YouTube\", \n    icon: Youtube, \n    href: \"https://youtube.com/excellenceacademy\",\n    color: \"hover:text-red-600\"\n  },\n  { \n    name: \"Twitter\", \n    icon: Twitter, \n    href: \"https://twitter.com/excellenceacademy\",\n    color: \"hover:text-blue-400\"\n  }\n];\n\nconst contactInfo = [\n  {\n    icon: Phone,\n    title: \"Phone\",\n    details: [\"+977-21-123456\", \"+977-**********\"]\n  },\n  {\n    icon: Mail,\n    title: \"Email\",\n    details: [\"<EMAIL>\", \"<EMAIL>\"]\n  },\n  {\n    icon: MapPin,\n    title: \"Address\",\n    details: [\"Main Road, Biratnagar-10\", \"Morang, Province 1, Nepal\"]\n  },\n  {\n    icon: Clock,\n    title: \"Hours\",\n    details: [\"Mon-Fri: 6:00 AM - 8:00 PM\", \"Sat-Sun: 6:00 AM - 6:00 PM\"]\n  }\n];\n\nexport default function Footer() {\n  const scrollToSection = (href: string) => {\n    if (href.startsWith('#')) {\n      const element = document.getElementById(href.substring(1));\n      element?.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Main Footer Content */}\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Company Info */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"flex items-center mb-6\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3\">\n                  <GraduationCap className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold\">Excellence Academy</h3>\n                  <p className=\"text-sm text-gray-400\">Biratnagar</p>\n                </div>\n              </div>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                Nurturing academic excellence since 2009. We are committed to providing \n                quality education and helping students achieve their dreams through \n                expert guidance and proven methodologies.\n              </p>\n              \n              {/* Social Links */}\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social, index) => {\n                  const IconComponent = social.icon;\n                  return (\n                    <a\n                      key={index}\n                      href={social.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className={`w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center transition-colors ${social.color} hover:bg-gray-700`}\n                      aria-label={social.name}\n                    >\n                      <IconComponent className=\"w-5 h-5\" />\n                    </a>\n                  );\n                })}\n              </div>\n            </div>\n\n            {/* Quick Links */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-6\">Quick Links</h4>\n              <ul className=\"space-y-3\">\n                {quickLinks.map((link, index) => (\n                  <li key={index}>\n                    <button\n                      onClick={() => scrollToSection(link.href)}\n                      className=\"text-gray-300 hover:text-white transition-colors text-left\"\n                    >\n                      {link.name}\n                    </button>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Courses */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-6\">Our Courses</h4>\n              <ul className=\"space-y-3\">\n                {courses.map((course, index) => (\n                  <li key={index}>\n                    <button\n                      onClick={() => scrollToSection(course.href)}\n                      className=\"text-gray-300 hover:text-white transition-colors text-left\"\n                    >\n                      {course.name}\n                    </button>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Contact Info */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-6\">Contact Info</h4>\n              <div className=\"space-y-4\">\n                {contactInfo.map((info, index) => {\n                  const IconComponent = info.icon;\n                  return (\n                    <div key={index} className=\"flex items-start space-x-3\">\n                      <div className=\"w-5 h-5 text-blue-400 mt-1 flex-shrink-0\">\n                        <IconComponent className=\"w-5 h-5\" />\n                      </div>\n                      <div>\n                        <h5 className=\"font-medium text-white mb-1\">{info.title}</h5>\n                        {info.details.map((detail, detailIndex) => (\n                          <p key={detailIndex} className=\"text-gray-300 text-sm\">\n                            {detail}\n                          </p>\n                        ))}\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </div>\n\n          {/* Newsletter Signup */}\n          <div className=\"mt-12 pt-8 border-t border-gray-800\">\n            <div className=\"text-center max-w-2xl mx-auto\">\n              <h4 className=\"text-xl font-semibold mb-4\">Stay Updated</h4>\n              <p className=\"text-gray-300 mb-6\">\n                Get the latest updates about admissions, courses, and academic achievements.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-white placeholder-gray-400\"\n                />\n                <Button className=\"bg-blue-600 hover:bg-blue-700\">\n                  Subscribe\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Achievement Highlights */}\n          <div className=\"mt-12 pt-8 border-t border-gray-800\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 text-center\">\n              <div>\n                <div className=\"text-2xl font-bold text-blue-400 mb-1\">15+</div>\n                <div className=\"text-gray-400 text-sm\">Years Experience</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-green-400 mb-1\">2000+</div>\n                <div className=\"text-gray-400 text-sm\">Students Graduated</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-yellow-400 mb-1\">95%</div>\n                <div className=\"text-gray-400 text-sm\">Success Rate</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-purple-400 mb-1\">50+</div>\n                <div className=\"text-gray-400 text-sm\">Expert Teachers</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <Separator className=\"bg-gray-800\" />\n\n      {/* Bottom Footer */}\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm text-center md:text-left\">\n              <p>\n                © {currentYear} Excellence Academy, Biratnagar. All rights reserved.\n              </p>\n              <p className=\"mt-1\">\n                Designed with <Heart className=\"w-4 h-4 text-red-500 inline mx-1\" /> for student success.\n              </p>\n            </div>\n            \n            <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-400\">\n              <button className=\"hover:text-white transition-colors\">\n                Privacy Policy\n              </button>\n              <button className=\"hover:text-white transition-colors\">\n                Terms of Service\n              </button>\n              <button className=\"hover:text-white transition-colors\">\n                Admission Policy\n              </button>\n            </div>\n          </div>\n\n          {/* Emergency Contact */}\n          <div className=\"mt-4 pt-4 border-t border-gray-800 text-center\">\n            <p className=\"text-gray-400 text-sm\">\n              Emergency Contact: <span className=\"text-red-400 font-medium\">+977-**********</span> (24/7)\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Back to Top Button */}\n      <button\n        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n        className=\"fixed bottom-8 right-8 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all duration-300 hover:scale-110 z-50\"\n        aria-label=\"Back to top\"\n      >\n        <div className=\"w-4 h-4 border-t-2 border-r-2 border-white transform rotate-[-45deg] mx-auto\"></div>\n      </button>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAkBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAY,MAAM;IAAS;IACnC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAe,MAAM;IAAW;IACxC;QAAE,MAAM;QAAiB,MAAM;IAAiB;IAChD;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,UAAU;IACd;QAAE,MAAM;QAAmB,MAAM;IAAW;IAC5C;QAAE,MAAM;QAAc,MAAM;IAAW;IACvC;QAAE,MAAM;QAAiB,MAAM;IAAW;IAC1C;QAAE,MAAM;QAAwB,MAAM;IAAW;IACjD;QAAE,MAAM;QAAoB,MAAM;IAAW;IAC7C;QAAE,MAAM;QAAqB,MAAM;IAAW;CAC/C;AAED,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,4MAAA,CAAA,YAAS;QACf,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,cAAc;IAClB;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAAkB;SAAkB;IAChD;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,SAAS;YAAC;YAA8B;SAAkC;IAC5E;IACA;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,SAAS;YAAC;YAA4B;SAA4B;IACpE;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAA8B;SAA6B;IACvE;CACD;AAEc,SAAS;IACtB,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,SAAS,CAAC;YACvD,SAAS,eAAe;gBAAE,UAAU;YAAS;QAC/C;IACF;IAEA,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoB;;;;;;sEAClC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAOlD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,gBAAgB,OAAO,IAAI;gDACjC,qBACE,8OAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,sFAAsF,EAAE,OAAO,KAAK,CAAC,kBAAkB,CAAC;oDACpI,cAAY,OAAO,IAAI;8DAEvB,cAAA,8OAAC;wDAAc,WAAU;;;;;;mDAPpB;;;;;4CAUX;;;;;;;;;;;;8CAKJ,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;sDACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;8DACC,cAAA,8OAAC;wDACC,SAAS,IAAM,gBAAgB,KAAK,IAAI;wDACxC,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL;;;;;;;;;;;;;;;;8CAaf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;sDACX,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;8DACC,cAAA,8OAAC;wDACC,SAAS,IAAM,gBAAgB,OAAO,IAAI;wDAC1C,WAAU;kEAET,OAAO,IAAI;;;;;;mDALP;;;;;;;;;;;;;;;;8CAaf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,MAAM;gDACtB,MAAM,gBAAgB,KAAK,IAAI;gDAC/B,qBACE,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAc,WAAU;;;;;;;;;;;sEAE3B,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA+B,KAAK,KAAK;;;;;;gEACtD,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,8OAAC;wEAAoB,WAAU;kFAC5B;uEADK;;;;;;;;;;;;mDAPJ;;;;;4CAcd;;;;;;;;;;;;;;;;;;sCAMN,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;sCAQxD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjD,8OAAC,qIAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BAGrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDACE;gDAAY;;;;;;;sDAEjB,8OAAC;4CAAE,WAAU;;gDAAO;8DACJ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqC;;;;;;;;;;;;;8CAIxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAAqC;;;;;;sDAGvD,8OAAC;4CAAO,WAAU;sDAAqC;;;;;;sDAGvD,8OAAC;4CAAO,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;sCAO3D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;kDAChB,8OAAC;wCAAK,WAAU;kDAA2B;;;;;;oCAAsB;;;;;;;;;;;;;;;;;;;;;;;0BAO5F,8OAAC;gBACC,SAAS,IAAM,OAAO,QAAQ,CAAC;wBAAE,KAAK;wBAAG,UAAU;oBAAS;gBAC5D,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}]}