{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/HeroSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/HeroSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/HeroSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2S,GACxU,yEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/HeroSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/HeroSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/HeroSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuR,GACpT,qDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/AboutSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/AboutSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/AboutSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/AboutSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/AboutSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/AboutSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/CoursesSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/CoursesSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/CoursesSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/CoursesSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/CoursesSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/CoursesSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/WhyChooseUsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/WhyChooseUsSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/WhyChooseUsSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/WhyChooseUsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/WhyChooseUsSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/WhyChooseUsSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 138, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/GallerySection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/GallerySection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/GallerySection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 158, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/GallerySection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/GallerySection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/GallerySection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/TestimonialsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/TestimonialsSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/TestimonialsSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/TestimonialsSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/TestimonialsSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/TestimonialsSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/ContactSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ContactSection.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ContactSection.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8S,GAC3U,4EACA", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/ContactSection.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/ContactSection.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/ContactSection.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0R,GACvT,wDACA", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 395, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 450, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/LocationSection.tsx"], "sourcesContent": ["import { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { \n  MapPin, \n  Navigation, \n  Bus, \n  Car, \n  Clock,\n  Phone,\n  ExternalLink\n} from \"lucide-react\";\n\nconst transportOptions = [\n  {\n    icon: Bus,\n    title: \"Public Transport\",\n    description: \"Regular bus services from all major areas of Biratnagar\",\n    routes: [\"Route 1: From Traffic Chowk\", \"Route 2: From Rani Chowk\", \"Route 3: From Hospital Area\"]\n  },\n  {\n    icon: Car,\n    title: \"Private Vehicle\",\n    description: \"Ample parking space available for cars and motorcycles\",\n    routes: [\"Free parking for students\", \"Secure parking area\", \"Easy access from main road\"]\n  },\n  {\n    icon: Navigation,\n    title: \"Walking Distance\",\n    description: \"Easily accessible on foot from nearby residential areas\",\n    routes: [\"5 min from Main Chowk\", \"10 min from Bus Park\", \"3 min from Bank Area\"]\n  }\n];\n\nconst nearbyLandmarks = [\n  { name: \"Biratnagar Hospital\", distance: \"500m\", direction: \"North\" },\n  { name: \"Traffic Police Office\", distance: \"300m\", direction: \"South\" },\n  { name: \"Nepal Bank\", distance: \"200m\", direction: \"East\" },\n  { name: \"Main Bus Park\", distance: \"800m\", direction: \"West\" },\n  { name: \"District Court\", distance: \"600m\", direction: \"Northeast\" },\n  { name: \"Shopping Complex\", distance: \"400m\", direction: \"Southwest\" }\n];\n\nexport default function LocationSection() {\n  const openGoogleMaps = () => {\n    // Coordinates for Biratnagar (approximate location)\n    const lat = 26.4525;\n    const lng = 87.2718;\n    const url = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}&query_place_id=Excellence+Academy+Biratnagar`;\n    window.open(url, '_blank');\n  };\n\n  const getDirections = () => {\n    const lat = 26.4525;\n    const lng = 87.2718;\n    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&destination_place_id=Excellence+Academy+Biratnagar`;\n    window.open(url, '_blank');\n  };\n\n  return (\n    <section className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Our Location\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Find Us in Biratnagar\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Conveniently located in the heart of Biratnagar, our institute is easily accessible \n              from all parts of the city. Visit us today to explore our facilities.\n            </p>\n          </div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Map Section */}\n            <div className=\"space-y-6\">\n              <Card className=\"overflow-hidden shadow-lg border-0\">\n                <div className=\"relative\">\n                  {/* Google Maps Embed */}\n                  <div className=\"aspect-video bg-gray-200 relative\">\n                    <iframe\n                      src=\"https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3570.8234567890123!2d87.2718!3d26.4525!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzA5LjAiTiA4N8KwMTYnMTguNSJF!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp\"\n                      width=\"100%\"\n                      height=\"100%\"\n                      style={{ border: 0 }}\n                      allowFullScreen\n                      loading=\"lazy\"\n                      referrerPolicy=\"no-referrer-when-downgrade\"\n                      title=\"Excellence Academy Location\"\n                      className=\"absolute inset-0\"\n                    ></iframe>\n                  </div>\n                  \n                  {/* Map Overlay */}\n                  <div className=\"absolute top-4 left-4 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-lg\">\n                    <div className=\"flex items-center text-gray-900\">\n                      <MapPin className=\"w-5 h-5 text-red-500 mr-2\" />\n                      <div>\n                        <div className=\"font-semibold text-sm\">Excellence Academy</div>\n                        <div className=\"text-xs text-gray-600\">Main Road, Biratnagar</div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                \n                <CardContent className=\"p-6\">\n                  <div className=\"flex flex-col sm:flex-row gap-3\">\n                    <Button \n                      onClick={openGoogleMaps}\n                      className=\"flex-1 flex items-center justify-center\"\n                    >\n                      <ExternalLink className=\"w-4 h-4 mr-2\" />\n                      View on Google Maps\n                    </Button>\n                    <Button \n                      variant=\"outline\"\n                      onClick={getDirections}\n                      className=\"flex-1 flex items-center justify-center\"\n                    >\n                      <Navigation className=\"w-4 h-4 mr-2\" />\n                      Get Directions\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Address Card */}\n              <Card className=\"shadow-lg border-0\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\n                    <MapPin className=\"w-5 h-5 text-red-500 mr-3\" />\n                    Complete Address\n                  </h3>\n                  <div className=\"space-y-2 text-gray-600\">\n                    <p className=\"font-semibold\">Excellence Academy</p>\n                    <p>Main Road, Ward No. 10</p>\n                    <p>Biratnagar, Morang</p>\n                    <p>Province 1, Nepal</p>\n                    <p className=\"flex items-center mt-3\">\n                      <Phone className=\"w-4 h-4 mr-2\" />\n                      +977-21-123456\n                    </p>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Location Information */}\n            <div className=\"space-y-6\">\n              {/* Transport Options */}\n              <Card className=\"shadow-lg border-0\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-6\">\n                    How to Reach Us\n                  </h3>\n                  <div className=\"space-y-6\">\n                    {transportOptions.map((option, index) => {\n                      const IconComponent = option.icon;\n                      return (\n                        <div key={index} className=\"flex items-start space-x-4\">\n                          <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0\">\n                            <IconComponent className=\"w-6 h-6 text-blue-600\" />\n                          </div>\n                          <div className=\"flex-1\">\n                            <h4 className=\"font-semibold text-gray-900 mb-2\">{option.title}</h4>\n                            <p className=\"text-gray-600 text-sm mb-3\">{option.description}</p>\n                            <ul className=\"space-y-1\">\n                              {option.routes.map((route, routeIndex) => (\n                                <li key={routeIndex} className=\"text-xs text-gray-500 flex items-center\">\n                                  <div className=\"w-1 h-1 bg-blue-400 rounded-full mr-2\"></div>\n                                  {route}\n                                </li>\n                              ))}\n                            </ul>\n                          </div>\n                        </div>\n                      );\n                    })}\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Nearby Landmarks */}\n              <Card className=\"shadow-lg border-0\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-6\">\n                    Nearby Landmarks\n                  </h3>\n                  <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n                    {nearbyLandmarks.map((landmark, index) => (\n                      <div key={index} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                        <div>\n                          <div className=\"font-medium text-gray-900 text-sm\">{landmark.name}</div>\n                          <div className=\"text-xs text-gray-500\">{landmark.direction}</div>\n                        </div>\n                        <Badge variant=\"outline\" className=\"text-xs\">\n                          {landmark.distance}\n                        </Badge>\n                      </div>\n                    ))}\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Visit Information */}\n              <Card className=\"shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4 flex items-center\">\n                    <Clock className=\"w-5 h-5 text-blue-600 mr-3\" />\n                    Visit Us\n                  </h3>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Office Hours</h4>\n                      <div className=\"space-y-1 text-sm text-gray-600\">\n                        <p>Monday - Friday: 6:00 AM - 8:00 PM</p>\n                        <p>Saturday - Sunday: 6:00 AM - 6:00 PM</p>\n                      </div>\n                    </div>\n                    <div>\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Best Time to Visit</h4>\n                      <p className=\"text-sm text-gray-600\">\n                        For admissions and inquiries, visit us between 10:00 AM - 5:00 PM \n                        when our counselors are available.\n                      </p>\n                    </div>\n                    <div className=\"pt-4 border-t border-blue-200\">\n                      <Button \n                        className=\"w-full\"\n                        onClick={() => {\n                          const contactSection = document.getElementById('contact');\n                          contactSection?.scrollIntoView({ behavior: 'smooth' });\n                        }}\n                      >\n                        Schedule a Visit\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n\n          {/* Additional Information */}\n          <div className=\"mt-16 text-center\">\n            <Card className=\"shadow-lg border-0 bg-white\">\n              <CardContent className=\"p-8\">\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                  Can't Find Us?\n                </h3>\n                <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n                  If you're having trouble locating our institute, don't hesitate to call us. \n                  Our staff will be happy to guide you with detailed directions.\n                </p>\n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n                  <Button \n                    size=\"lg\"\n                    onClick={() => window.open('tel:+977-21-123456', '_self')}\n                  >\n                    <Phone className=\"w-4 h-4 mr-2\" />\n                    Call for Directions\n                  </Button>\n                  <Button \n                    size=\"lg\"\n                    variant=\"outline\"\n                    onClick={getDirections}\n                  >\n                    <Navigation className=\"w-4 h-4 mr-2\" />\n                    GPS Navigation\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAUA,MAAM,mBAAmB;IACvB;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;YAA+B;YAA4B;SAA8B;IACpG;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;YAA6B;YAAuB;SAA6B;IAC5F;IACA;QACE,MAAM,8MAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;YAAyB;YAAwB;SAAuB;IACnF;CACD;AAED,MAAM,kBAAkB;IACtB;QAAE,MAAM;QAAuB,UAAU;QAAQ,WAAW;IAAQ;IACpE;QAAE,MAAM;QAAyB,UAAU;QAAQ,WAAW;IAAQ;IACtE;QAAE,MAAM;QAAc,UAAU;QAAQ,WAAW;IAAO;IAC1D;QAAE,MAAM;QAAiB,UAAU;QAAQ,WAAW;IAAO;IAC7D;QAAE,MAAM;QAAkB,UAAU;QAAQ,WAAW;IAAY;IACnE;QAAE,MAAM;QAAoB,UAAU;QAAQ,WAAW;IAAY;CACtE;AAEc,SAAS;IACtB,MAAM,iBAAiB;QACrB,oDAAoD;QACpD,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM,CAAC,gDAAgD,EAAE,IAAI,CAAC,EAAE,IAAI,6CAA6C,CAAC;QACxH,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,MAAM,gBAAgB;QACpB,MAAM,MAAM;QACZ,MAAM,MAAM;QACZ,MAAM,MAAM,CAAC,mDAAmD,EAAE,IAAI,CAAC,EAAE,IAAI,mDAAmD,CAAC;QACjI,OAAO,IAAI,CAAC,KAAK;IACnB;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,8OAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,8OAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,KAAI;4DACJ,OAAM;4DACN,QAAO;4DACP,OAAO;gEAAE,QAAQ;4DAAE;4DACnB,eAAe;4DACf,SAAQ;4DACR,gBAAe;4DACf,OAAM;4DACN,WAAU;;;;;;;;;;;kEAKd,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0MAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;sFACvC,8OAAC;4EAAI,WAAU;sFAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAM/C,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAS;4DACT,WAAU;;8EAEV,8OAAC,sNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAG3C,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,SAAS;4DACT,WAAU;;8EAEV,8OAAC,8MAAA,CAAA,aAAU;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;kDAQ/C,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAA8B;;;;;;;8DAGlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;sEAC7B,8OAAC;sEAAE;;;;;;sEACH,8OAAC;sEAAE;;;;;;sEACH,8OAAC;sEAAE;;;;;;sEACH,8OAAC;4DAAE,WAAU;;8EACX,8OAAC,oMAAA,CAAA,QAAK;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAS5C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAGrD,8OAAC;oDAAI,WAAU;8DACZ,iBAAiB,GAAG,CAAC,CAAC,QAAQ;wDAC7B,MAAM,gBAAgB,OAAO,IAAI;wDACjC,qBACE,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAc,WAAU;;;;;;;;;;;8EAE3B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAAoC,OAAO,KAAK;;;;;;sFAC9D,8OAAC;4EAAE,WAAU;sFAA8B,OAAO,WAAW;;;;;;sFAC7D,8OAAC;4EAAG,WAAU;sFACX,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,2BACzB,8OAAC;oFAAoB,WAAU;;sGAC7B,8OAAC;4FAAI,WAAU;;;;;;wFACd;;mFAFM;;;;;;;;;;;;;;;;;2DATP;;;;;oDAkBd;;;;;;;;;;;;;;;;;kDAMN,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAGrD,8OAAC;oDAAI,WAAU;8DACZ,gBAAgB,GAAG,CAAC,CAAC,UAAU,sBAC9B,8OAAC;4DAAgB,WAAU;;8EACzB,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAqC,SAAS,IAAI;;;;;;sFACjE,8OAAC;4EAAI,WAAU;sFAAyB,SAAS,SAAS;;;;;;;;;;;;8EAE5D,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAU,WAAU;8EAChC,SAAS,QAAQ;;;;;;;2DANZ;;;;;;;;;;;;;;;;;;;;;kDAelB,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAA+B;;;;;;;8DAGlD,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;sFAAE;;;;;;sFACH,8OAAC;sFAAE;;;;;;;;;;;;;;;;;;sEAGP,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAmC;;;;;;8EACjD,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAKvC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEACL,WAAU;gEACV,SAAS;oEACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;oEAC/C,gBAAgB,eAAe;wEAAE,UAAU;oEAAS;gEACtD;0EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDAIpD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,OAAO,IAAI,CAAC,sBAAsB;;kEAEjD,8OAAC,oMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGpC,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;;kEAET,8OAAC,8MAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW3D", "debugId": null}}, {"offset": {"line": 1256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/separator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx <module evaluation>\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA", "debugId": null}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/separator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6CACA", "debugId": null}}, {"offset": {"line": 1280, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/Footer.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { \n  GraduationCap,\n  Phone, \n  Mail, \n  MapPin, \n  Clock,\n  Facebook,\n  Instagram,\n  Youtube,\n  Twitter,\n  ExternalLink,\n  Heart\n} from \"lucide-react\";\n\nconst quickLinks = [\n  { name: \"About Us\", href: \"#about\" },\n  { name: \"Courses\", href: \"#courses\" },\n  { name: \"Why Choose Us\", href: \"#why-choose-us\" },\n  { name: \"Gallery\", href: \"#gallery\" },\n  { name: \"Testimonials\", href: \"#testimonials\" },\n  { name: \"Contact\", href: \"#contact\" }\n];\n\nconst courses = [\n  { name: \"SEE Preparation\", href: \"#contact\" },\n  { name: \"+2 Science\", href: \"#contact\" },\n  { name: \"+2 Management\", href: \"#contact\" },\n  { name: \"Engineering Entrance\", href: \"#contact\" },\n  { name: \"Medical Entrance\", href: \"#contact\" },\n  { name: \"Foundation Course\", href: \"#contact\" }\n];\n\nconst socialLinks = [\n  { \n    name: \"Facebook\", \n    icon: Facebook, \n    href: \"https://facebook.com/excellenceacademy\",\n    color: \"hover:text-blue-600\"\n  },\n  { \n    name: \"Instagram\", \n    icon: Instagram, \n    href: \"https://instagram.com/excellenceacademy\",\n    color: \"hover:text-pink-600\"\n  },\n  { \n    name: \"YouTube\", \n    icon: Youtube, \n    href: \"https://youtube.com/excellenceacademy\",\n    color: \"hover:text-red-600\"\n  },\n  { \n    name: \"Twitter\", \n    icon: Twitter, \n    href: \"https://twitter.com/excellenceacademy\",\n    color: \"hover:text-blue-400\"\n  }\n];\n\nconst contactInfo = [\n  {\n    icon: Phone,\n    title: \"Phone\",\n    details: [\"+977-21-123456\", \"+977-**********\"]\n  },\n  {\n    icon: Mail,\n    title: \"Email\",\n    details: [\"<EMAIL>\", \"<EMAIL>\"]\n  },\n  {\n    icon: MapPin,\n    title: \"Address\",\n    details: [\"Main Road, Biratnagar-10\", \"Morang, Province 1, Nepal\"]\n  },\n  {\n    icon: Clock,\n    title: \"Hours\",\n    details: [\"Mon-Fri: 6:00 AM - 8:00 PM\", \"Sat-Sun: 6:00 AM - 6:00 PM\"]\n  }\n];\n\nexport default function Footer() {\n  const scrollToSection = (href: string) => {\n    if (href.startsWith('#')) {\n      const element = document.getElementById(href.substring(1));\n      element?.scrollIntoView({ behavior: 'smooth' });\n    }\n  };\n\n  const currentYear = new Date().getFullYear();\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      {/* Main Footer Content */}\n      <div className=\"container mx-auto px-4 py-16\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {/* Company Info */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"flex items-center mb-6\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3\">\n                  <GraduationCap className=\"w-6 h-6 text-white\" />\n                </div>\n                <div>\n                  <h3 className=\"text-xl font-bold\">Excellence Academy</h3>\n                  <p className=\"text-sm text-gray-400\">Biratnagar</p>\n                </div>\n              </div>\n              <p className=\"text-gray-300 mb-6 leading-relaxed\">\n                Nurturing academic excellence since 2009. We are committed to providing \n                quality education and helping students achieve their dreams through \n                expert guidance and proven methodologies.\n              </p>\n              \n              {/* Social Links */}\n              <div className=\"flex space-x-4\">\n                {socialLinks.map((social, index) => {\n                  const IconComponent = social.icon;\n                  return (\n                    <a\n                      key={index}\n                      href={social.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className={`w-10 h-10 bg-gray-800 rounded-full flex items-center justify-center transition-colors ${social.color} hover:bg-gray-700`}\n                      aria-label={social.name}\n                    >\n                      <IconComponent className=\"w-5 h-5\" />\n                    </a>\n                  );\n                })}\n              </div>\n            </div>\n\n            {/* Quick Links */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-6\">Quick Links</h4>\n              <ul className=\"space-y-3\">\n                {quickLinks.map((link, index) => (\n                  <li key={index}>\n                    <button\n                      onClick={() => scrollToSection(link.href)}\n                      className=\"text-gray-300 hover:text-white transition-colors text-left\"\n                    >\n                      {link.name}\n                    </button>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Courses */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-6\">Our Courses</h4>\n              <ul className=\"space-y-3\">\n                {courses.map((course, index) => (\n                  <li key={index}>\n                    <button\n                      onClick={() => scrollToSection(course.href)}\n                      className=\"text-gray-300 hover:text-white transition-colors text-left\"\n                    >\n                      {course.name}\n                    </button>\n                  </li>\n                ))}\n              </ul>\n            </div>\n\n            {/* Contact Info */}\n            <div>\n              <h4 className=\"text-lg font-semibold mb-6\">Contact Info</h4>\n              <div className=\"space-y-4\">\n                {contactInfo.map((info, index) => {\n                  const IconComponent = info.icon;\n                  return (\n                    <div key={index} className=\"flex items-start space-x-3\">\n                      <div className=\"w-5 h-5 text-blue-400 mt-1 flex-shrink-0\">\n                        <IconComponent className=\"w-5 h-5\" />\n                      </div>\n                      <div>\n                        <h5 className=\"font-medium text-white mb-1\">{info.title}</h5>\n                        {info.details.map((detail, detailIndex) => (\n                          <p key={detailIndex} className=\"text-gray-300 text-sm\">\n                            {detail}\n                          </p>\n                        ))}\n                      </div>\n                    </div>\n                  );\n                })}\n              </div>\n            </div>\n          </div>\n\n          {/* Newsletter Signup */}\n          <div className=\"mt-12 pt-8 border-t border-gray-800\">\n            <div className=\"text-center max-w-2xl mx-auto\">\n              <h4 className=\"text-xl font-semibold mb-4\">Stay Updated</h4>\n              <p className=\"text-gray-300 mb-6\">\n                Get the latest updates about admissions, courses, and academic achievements.\n              </p>\n              <div className=\"flex flex-col sm:flex-row gap-4 max-w-md mx-auto\">\n                <input\n                  type=\"email\"\n                  placeholder=\"Enter your email\"\n                  className=\"flex-1 px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-white placeholder-gray-400\"\n                />\n                <Button className=\"bg-blue-600 hover:bg-blue-700\">\n                  Subscribe\n                </Button>\n              </div>\n            </div>\n          </div>\n\n          {/* Achievement Highlights */}\n          <div className=\"mt-12 pt-8 border-t border-gray-800\">\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 text-center\">\n              <div>\n                <div className=\"text-2xl font-bold text-blue-400 mb-1\">15+</div>\n                <div className=\"text-gray-400 text-sm\">Years Experience</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-green-400 mb-1\">2000+</div>\n                <div className=\"text-gray-400 text-sm\">Students Graduated</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-yellow-400 mb-1\">95%</div>\n                <div className=\"text-gray-400 text-sm\">Success Rate</div>\n              </div>\n              <div>\n                <div className=\"text-2xl font-bold text-purple-400 mb-1\">50+</div>\n                <div className=\"text-gray-400 text-sm\">Expert Teachers</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <Separator className=\"bg-gray-800\" />\n\n      {/* Bottom Footer */}\n      <div className=\"container mx-auto px-4 py-6\">\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-gray-400 text-sm text-center md:text-left\">\n              <p>\n                © {currentYear} Excellence Academy, Biratnagar. All rights reserved.\n              </p>\n              <p className=\"mt-1\">\n                Designed with <Heart className=\"w-4 h-4 text-red-500 inline mx-1\" /> for student success.\n              </p>\n            </div>\n            \n            <div className=\"flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-6 text-sm text-gray-400\">\n              <button className=\"hover:text-white transition-colors\">\n                Privacy Policy\n              </button>\n              <button className=\"hover:text-white transition-colors\">\n                Terms of Service\n              </button>\n              <button className=\"hover:text-white transition-colors\">\n                Admission Policy\n              </button>\n            </div>\n          </div>\n\n          {/* Emergency Contact */}\n          <div className=\"mt-4 pt-4 border-t border-gray-800 text-center\">\n            <p className=\"text-gray-400 text-sm\">\n              Emergency Contact: <span className=\"text-red-400 font-medium\">+977-**********</span> (24/7)\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Back to Top Button */}\n      <button\n        onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}\n        className=\"fixed bottom-8 right-8 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg transition-all duration-300 hover:scale-110 z-50\"\n        aria-label=\"Back to top\"\n      >\n        <div className=\"w-4 h-4 border-t-2 border-r-2 border-white transform rotate-[-45deg] mx-auto\"></div>\n      </button>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AAcA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAY,MAAM;IAAS;IACnC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAiB,MAAM;IAAiB;IAChD;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAgB,MAAM;IAAgB;IAC9C;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,UAAU;IACd;QAAE,MAAM;QAAmB,MAAM;IAAW;IAC5C;QAAE,MAAM;QAAc,MAAM;IAAW;IACvC;QAAE,MAAM;QAAiB,MAAM;IAAW;IAC1C;QAAE,MAAM;QAAwB,MAAM;IAAW;IACjD;QAAE,MAAM;QAAoB,MAAM;IAAW;IAC7C;QAAE,MAAM;QAAqB,MAAM;IAAW;CAC/C;AAED,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,4MAAA,CAAA,YAAS;QACf,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,MAAM;QACN,OAAO;IACT;IACA;QACE,MAAM;QACN,MAAM,wMAAA,CAAA,UAAO;QACb,MAAM;QACN,OAAO;IACT;CACD;AAED,MAAM,cAAc;IAClB;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAAkB;SAAkB;IAChD;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,SAAS;YAAC;YAA8B;SAAkC;IAC5E;IACA;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,SAAS;YAAC;YAA4B;SAA4B;IACpE;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAA8B;SAA6B;IACvE;CACD;AAEc,SAAS;IACtB,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,UAAU,CAAC,MAAM;YACxB,MAAM,UAAU,SAAS,cAAc,CAAC,KAAK,SAAS,CAAC;YACvD,SAAS,eAAe;gBAAE,UAAU;YAAS;QAC/C;IACF;IAEA,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;;;;;;8DAE3B,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAoB;;;;;;sEAClC,8OAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;sDAGzC,8OAAC;4CAAE,WAAU;sDAAqC;;;;;;sDAOlD,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,QAAQ;gDACxB,MAAM,gBAAgB,OAAO,IAAI;gDACjC,qBACE,8OAAC;oDAEC,MAAM,OAAO,IAAI;oDACjB,QAAO;oDACP,KAAI;oDACJ,WAAW,CAAC,sFAAsF,EAAE,OAAO,KAAK,CAAC,kBAAkB,CAAC;oDACpI,cAAY,OAAO,IAAI;8DAEvB,cAAA,8OAAC;wDAAc,WAAU;;;;;;mDAPpB;;;;;4CAUX;;;;;;;;;;;;8CAKJ,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;sDACX,WAAW,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC;8DACC,cAAA,8OAAC;wDACC,SAAS,IAAM,gBAAgB,KAAK,IAAI;wDACxC,WAAU;kEAET,KAAK,IAAI;;;;;;mDALL;;;;;;;;;;;;;;;;8CAaf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAG,WAAU;sDACX,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,8OAAC;8DACC,cAAA,8OAAC;wDACC,SAAS,IAAM,gBAAgB,OAAO,IAAI;wDAC1C,WAAU;kEAET,OAAO,IAAI;;;;;;mDALP;;;;;;;;;;;;;;;;8CAaf,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAI,WAAU;sDACZ,YAAY,GAAG,CAAC,CAAC,MAAM;gDACtB,MAAM,gBAAgB,KAAK,IAAI;gDAC/B,qBACE,8OAAC;oDAAgB,WAAU;;sEACzB,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAc,WAAU;;;;;;;;;;;sEAE3B,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA+B,KAAK,KAAK;;;;;;gEACtD,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,8OAAC;wEAAoB,WAAU;kFAC5B;uEADK;;;;;;;;;;;;mDAPJ;;;;;4CAcd;;;;;;;;;;;;;;;;;;sCAMN,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC,kIAAA,CAAA,SAAM;gDAAC,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;;sCAQxD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAwC;;;;;;0DACvD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAAyC;;;;;;0DACxD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;kDAEzC,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;0DAA0C;;;;;;0DACzD,8OAAC;gDAAI,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOjD,8OAAC,qIAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BAGrB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;gDAAE;gDACE;gDAAY;;;;;;;sDAEjB,8OAAC;4CAAE,WAAU;;gDAAO;8DACJ,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAqC;;;;;;;;;;;;;8CAIxE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAAqC;;;;;;sDAGvD,8OAAC;4CAAO,WAAU;sDAAqC;;;;;;sDAGvD,8OAAC;4CAAO,WAAU;sDAAqC;;;;;;;;;;;;;;;;;;sCAO3D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAAwB;kDAChB,8OAAC;wCAAK,WAAU;kDAA2B;;;;;;oCAAsB;;;;;;;;;;;;;;;;;;;;;;;0BAO5F,8OAAC;gBACC,SAAS,IAAM,OAAO,QAAQ,CAAC;wBAAE,KAAK;wBAAG,UAAU;oBAAS;gBAC5D,WAAU;gBACV,cAAW;0BAEX,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB", "debugId": null}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/app/page.tsx"], "sourcesContent": ["import HeroSection from \"@/components/sections/HeroSection\";\nimport AboutSection from \"@/components/sections/AboutSection\";\nimport CoursesSection from \"@/components/sections/CoursesSection\";\nimport WhyChooseUsSection from \"@/components/sections/WhyChooseUsSection\";\nimport GallerySection from \"@/components/sections/GallerySection\";\nimport TestimonialsSection from \"@/components/sections/TestimonialsSection\";\nimport ContactSection from \"@/components/sections/ContactSection\";\nimport LocationSection from \"@/components/sections/LocationSection\";\nimport Footer from \"@/components/sections/Footer\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      <HeroSection />\n      <AboutSection />\n      <CoursesSection />\n      <WhyChooseUsSection />\n      <GallerySection />\n      <TestimonialsSection />\n      <ContactSection />\n      <LocationSection />\n      <Footer />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,6IAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,8IAAA,CAAA,UAAY;;;;;0BACb,8OAAC,gJAAA,CAAA,UAAc;;;;;0BACf,8OAAC,oJAAA,CAAA,UAAkB;;;;;0BACnB,8OAAC,gJAAA,CAAA,UAAc;;;;;0BACf,8OAAC,qJAAA,CAAA,UAAmB;;;;;0BACpB,8OAAC,gJAAA,CAAA,UAAc;;;;;0BACf,8OAAC,iJAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,wIAAA,CAAA,UAAM;;;;;;;;;;;AAGb", "debugId": null}}]}