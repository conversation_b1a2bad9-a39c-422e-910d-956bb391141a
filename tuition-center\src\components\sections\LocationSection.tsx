"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  MapPin, 
  Navigation, 
  Bus, 
  Car, 
  Clock,
  Phone,
  ExternalLink
} from "lucide-react";

const transportOptions = [
  {
    icon: Bus,
    title: "Public Transport",
    description: "Regular bus services from all major areas of Biratnagar",
    routes: ["Route 1: From Traffic Chowk", "Route 2: From Rani Chowk", "Route 3: From Hospital Area"]
  },
  {
    icon: Car,
    title: "Private Vehicle",
    description: "Ample parking space available for cars and motorcycles",
    routes: ["Free parking for students", "Secure parking area", "Easy access from main road"]
  },
  {
    icon: Navigation,
    title: "Walking Distance",
    description: "Easily accessible on foot from nearby residential areas",
    routes: ["5 min from Main Chowk", "10 min from Bus Park", "3 min from Bank Area"]
  }
];

const nearbyLandmarks = [
  { name: "Biratnagar Hospital", distance: "500m", direction: "North" },
  { name: "Traffic Police Office", distance: "300m", direction: "South" },
  { name: "Nepal Bank", distance: "200m", direction: "East" },
  { name: "Main Bus Park", distance: "800m", direction: "West" },
  { name: "District Court", distance: "600m", direction: "Northeast" },
  { name: "Shopping Complex", distance: "400m", direction: "Southwest" }
];

export default function LocationSection() {
  const openGoogleMaps = () => {
    // Coordinates for Biratnagar (approximate location)
    const lat = 26.4525;
    const lng = 87.2718;
    const url = `https://www.google.com/maps/search/?api=1&query=${lat},${lng}&query_place_id=Excellence+Academy+Biratnagar`;
    window.open(url, '_blank');
  };

  const getDirections = () => {
    const lat = 26.4525;
    const lng = 87.2718;
    const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&destination_place_id=Excellence+Academy+Biratnagar`;
    window.open(url, '_blank');
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4 text-blue-600 bg-blue-100">
              Our Location
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Find Us in Biratnagar
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Conveniently located in the heart of Biratnagar, our institute is easily accessible 
              from all parts of the city. Visit us today to explore our facilities.
            </p>
          </div>

          <div className="grid lg:grid-cols-2 gap-12">
            {/* Map Section */}
            <div className="space-y-6">
              <Card className="overflow-hidden shadow-lg border-0">
                <div className="relative">
                  {/* Google Maps Embed */}
                  <div className="aspect-video bg-gray-200 relative">
                    <iframe
                      src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3570.8234567890123!2d87.2718!3d26.4525!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zMjbCsDI3JzA5LjAiTiA4N8KwMTYnMTguNSJF!5e0!3m2!1sen!2snp!4v1234567890123!5m2!1sen!2snp"
                      width="100%"
                      height="100%"
                      style={{ border: 0 }}
                      allowFullScreen
                      loading="lazy"
                      referrerPolicy="no-referrer-when-downgrade"
                      title="Excellence Academy Location"
                      className="absolute inset-0"
                    ></iframe>
                  </div>
                  
                  {/* Map Overlay */}
                  <div className="absolute top-4 left-4 bg-white bg-opacity-90 backdrop-blur-sm rounded-lg p-3 shadow-lg">
                    <div className="flex items-center text-gray-900">
                      <MapPin className="w-5 h-5 text-red-500 mr-2" />
                      <div>
                        <div className="font-semibold text-sm">Excellence Academy</div>
                        <div className="text-xs text-gray-600">Main Road, Biratnagar</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <CardContent className="p-6">
                  <div className="flex flex-col sm:flex-row gap-3">
                    <Button 
                      onClick={openGoogleMaps}
                      className="flex-1 flex items-center justify-center"
                    >
                      <ExternalLink className="w-4 h-4 mr-2" />
                      View on Google Maps
                    </Button>
                    <Button 
                      variant="outline"
                      onClick={getDirections}
                      className="flex-1 flex items-center justify-center"
                    >
                      <Navigation className="w-4 h-4 mr-2" />
                      Get Directions
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Address Card */}
              <Card className="shadow-lg border-0">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <MapPin className="w-5 h-5 text-red-500 mr-3" />
                    Complete Address
                  </h3>
                  <div className="space-y-2 text-gray-600">
                    <p className="font-semibold">Excellence Academy</p>
                    <p>Main Road, Ward No. 10</p>
                    <p>Biratnagar, Morang</p>
                    <p>Province 1, Nepal</p>
                    <p className="flex items-center mt-3">
                      <Phone className="w-4 h-4 mr-2" />
                      +977-21-123456
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Location Information */}
            <div className="space-y-6">
              {/* Transport Options */}
              <Card className="shadow-lg border-0">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">
                    How to Reach Us
                  </h3>
                  <div className="space-y-6">
                    {transportOptions.map((option, index) => {
                      const IconComponent = option.icon;
                      return (
                        <div key={index} className="flex items-start space-x-4">
                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                            <IconComponent className="w-6 h-6 text-blue-600" />
                          </div>
                          <div className="flex-1">
                            <h4 className="font-semibold text-gray-900 mb-2">{option.title}</h4>
                            <p className="text-gray-600 text-sm mb-3">{option.description}</p>
                            <ul className="space-y-1">
                              {option.routes.map((route, routeIndex) => (
                                <li key={routeIndex} className="text-xs text-gray-500 flex items-center">
                                  <div className="w-1 h-1 bg-blue-400 rounded-full mr-2"></div>
                                  {route}
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>

              {/* Nearby Landmarks */}
              <Card className="shadow-lg border-0">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-6">
                    Nearby Landmarks
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {nearbyLandmarks.map((landmark, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <div className="font-medium text-gray-900 text-sm">{landmark.name}</div>
                          <div className="text-xs text-gray-500">{landmark.direction}</div>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {landmark.distance}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Visit Information */}
              <Card className="shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <Clock className="w-5 h-5 text-blue-600 mr-3" />
                    Visit Us
                  </h3>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Office Hours</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        <p>Monday - Friday: 6:00 AM - 8:00 PM</p>
                        <p>Saturday - Sunday: 6:00 AM - 6:00 PM</p>
                      </div>
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900 mb-2">Best Time to Visit</h4>
                      <p className="text-sm text-gray-600">
                        For admissions and inquiries, visit us between 10:00 AM - 5:00 PM 
                        when our counselors are available.
                      </p>
                    </div>
                    <div className="pt-4 border-t border-blue-200">
                      <Button 
                        className="w-full"
                        onClick={() => {
                          const contactSection = document.getElementById('contact');
                          contactSection?.scrollIntoView({ behavior: 'smooth' });
                        }}
                      >
                        Schedule a Visit
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Additional Information */}
          <div className="mt-16 text-center">
            <Card className="shadow-lg border-0 bg-white">
              <CardContent className="p-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  Can&apos;t Find Us?
                </h3>
                <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                  If you&apos;re having trouble locating our institute, don&apos;t hesitate to call us.
                  Our staff will be happy to guide you with detailed directions.
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button 
                    size="lg"
                    onClick={() => window.open('tel:+977-21-123456', '_self')}
                  >
                    <Phone className="w-4 h-4 mr-2" />
                    Call for Directions
                  </Button>
                  <Button 
                    size="lg"
                    variant="outline"
                    onClick={getDirections}
                  >
                    <Navigation className="w-4 h-4 mr-2" />
                    GPS Navigation
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
