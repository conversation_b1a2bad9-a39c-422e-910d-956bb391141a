"use client";

import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Target, Heart, BookOpen, Users, Award, Clock } from "lucide-react";

export default function AboutSection() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4 text-blue-600 bg-blue-100">
              About Excellence Academy
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Nurturing Academic Excellence Since 2009
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Located in the heart of Biratnagar, we have been the trusted partner in thousands of students&apos;
              academic journeys, helping them achieve their dreams through quality education and personalized guidance.
            </p>
          </div>

          {/* Main Content Grid */}
          <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
            {/* Left Content */}
            <div className="space-y-8 order-2 lg:order-1">
              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <Target className="w-6 h-6 text-blue-600 mr-3" />
                  Our Mission
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  To provide world-class education that empowers students to excel in their academic pursuits 
                  and develop into confident, capable individuals ready to face future challenges. We believe 
                  every student has unique potential that deserves to be nurtured and developed.
                </p>
              </div>

              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <Heart className="w-6 h-6 text-red-500 mr-3" />
                  Our Values
                </h3>
                <ul className="space-y-2 text-gray-600">
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                    Excellence in teaching and learning
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                    Individual attention to every student
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                    Integrity and ethical practices
                  </li>
                  <li className="flex items-center">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mr-3"></div>
                    Continuous innovation in education
                  </li>
                </ul>
              </div>

              <div>
                <h3 className="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                  <BookOpen className="w-6 h-6 text-green-600 mr-3" />
                  Teaching Approach
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  Our proven methodology combines traditional teaching excellence with modern educational 
                  techniques. We focus on conceptual clarity, regular practice, and continuous assessment 
                  to ensure comprehensive learning and outstanding results.
                </p>
              </div>
            </div>

            {/* Right Content - Image and Stats */}
            <div className="order-1 lg:order-2 space-y-6">
              {/* Hero Image */}
              <div className="relative rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="https://images.unsplash.com/photo-1509062522246-3755977927d7?w=600&h=400&fit=crop"
                  alt="Students in modern classroom"
                  width={600}
                  height={320}
                  className="w-full h-80 object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent"></div>
                <div className="absolute bottom-4 left-4 text-white">
                  <p className="text-sm font-medium">Excellence in Education Since 2009</p>
                </div>
              </div>

              {/* Stats Cards */}
              <div className="grid grid-cols-2 gap-4">
                <Card className="text-center p-4 hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    <Users className="w-8 h-8 text-blue-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 mb-1">2000+</div>
                    <div className="text-gray-600 text-sm">Students</div>
                  </CardContent>
                </Card>

                <Card className="text-center p-4 hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    <Award className="w-8 h-8 text-yellow-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 mb-1">95%</div>
                    <div className="text-gray-600 text-sm">Success Rate</div>
                  </CardContent>
                </Card>

                <Card className="text-center p-4 hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    <Clock className="w-8 h-8 text-green-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 mb-1">15+</div>
                    <div className="text-gray-600 text-sm">Years</div>
                  </CardContent>
                </Card>

                <Card className="text-center p-4 hover:shadow-lg transition-shadow">
                  <CardContent className="p-0">
                    <BookOpen className="w-8 h-8 text-purple-600 mx-auto mb-2" />
                    <div className="text-2xl font-bold text-gray-900 mb-1">50+</div>
                    <div className="text-gray-600 text-sm">Teachers</div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>

          {/* Experience Timeline */}
          <div className="bg-white rounded-2xl p-8 shadow-lg">
            <h3 className="text-2xl font-bold text-gray-900 mb-8 text-center">Our Journey</h3>
            <div className="grid md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">2009</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Foundation</h4>
                <p className="text-gray-600 text-sm">Started with a vision to provide quality education in Biratnagar</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-green-600">2015</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Expansion</h4>
                <p className="text-gray-600 text-sm">Expanded to include entrance exam preparation courses</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-purple-600">2024</span>
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">Excellence</h4>
                <p className="text-gray-600 text-sm">Recognized as the leading coaching institute in the region</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
