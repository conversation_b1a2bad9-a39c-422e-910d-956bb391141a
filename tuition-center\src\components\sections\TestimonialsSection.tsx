"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Star, Quote, ChevronLeft, ChevronRight, User } from "lucide-react";

const testimonials = [
  {
    id: 1,
    name: "<PERSON><PERSON>",
    role: "SEE Graduate 2023",
    course: "SEE Preparation",
    rating: 5,
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face",
    testimonial: "Excellence Academy completely transformed my approach to studying. The teachers here don't just teach subjects; they build confidence. I scored 3.95 GPA in SEE, which seemed impossible before joining here. The individual attention and regular tests helped me identify my weak areas and improve them systematically.",
    achievement: "3.95 GPA in SEE"
  },
  {
    id: 2,
    name: "<PERSON><PERSON>",
    role: "Engineering Student",
    course: "Engineering Entrance",
    rating: 5,
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face",
    testimonial: "The entrance preparation course at Excellence Academy is simply outstanding. The faculty's deep understanding of exam patterns and their teaching methodology helped me crack IOE entrance exam. The mock tests were exactly like the real exam, which boosted my confidence tremendously.",
    achievement: "IOE Pulchowk Admission"
  },
  {
    id: 3,
    name: "Sunita Rai",
    role: "Parent",
    course: "+2 Science",
    rating: 5,
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face",
    testimonial: "As a parent, I was initially worried about my daughter's performance in +2 Science. But Excellence Academy's supportive environment and excellent teaching staff helped her excel. The regular parent-teacher meetings kept me informed about her progress. She's now pursuing MBBS!",
    achievement: "Daughter got MBBS admission"
  },
  {
    id: 4,
    name: "Amit Kumar",
    role: "+2 Management Graduate",
    course: "+2 Management",
    rating: 5,
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
    testimonial: "The practical approach to teaching business studies and economics at Excellence Academy is remarkable. The teachers use real-world examples that made complex concepts easy to understand. I secured first division and got admission to my preferred college for BBS.",
    achievement: "First Division in +2"
  },
  {
    id: 5,
    name: "Anita Gurung",
    role: "Medical Student",
    course: "Medical Entrance",
    rating: 5,
    image: "https://images.unsplash.com/photo-**********-94ddf0286df2?w=100&h=100&fit=crop&crop=face",
    testimonial: "The medical entrance preparation at Excellence Academy is top-notch. The biology faculty's expertise and the comprehensive study materials provided gave me the edge I needed. The regular practice sessions and doubt-clearing classes were incredibly helpful in my MBBS entrance preparation.",
    achievement: "MBBS Admission Secured"
  },
  {
    id: 6,
    name: "Krishna Bahadur",
    role: "Parent",
    course: "Foundation Course",
    rating: 5,
    image: "https://images.unsplash.com/photo-**********-0b93528c311a?w=100&h=100&fit=crop&crop=face",
    testimonial: "My son was struggling with mathematics in class 9. Excellence Academy's foundation course not only improved his grades but also built his confidence. The teachers are patient and understanding. Now he's one of the top students in his class. Highly recommended!",
    achievement: "Son improved from C to A grade"
  }
];

export default function TestimonialsSection() {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlaying) return;
    
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => 
        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const goToNext = () => {
    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);
  };

  const goToPrev = () => {
    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);
  };

  const goToSlide = (index: number) => {
    setCurrentIndex(index);
  };

  const currentTestimonial = testimonials[currentIndex];

  return (
    <section className="py-20 relative overflow-hidden">
      {/* Background Image */}
      <div className="absolute inset-0">
        <img
          src="https://images.unsplash.com/photo-1523240795612-9a054b0db644?w=1920&h=1080&fit=crop"
          alt="Happy students"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/95 to-indigo-100/95"></div>
      </div>

      <div className="relative z-10">
        <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4 text-blue-600 bg-blue-100">
              Testimonials
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              What Our Students Say
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Don't just take our word for it. Hear from our successful students and parents 
              who have experienced the Excellence Academy difference firsthand.
            </p>
          </div>

          {/* Main Testimonial Card */}
          <div className="relative mb-12">
            <Card 
              className="max-w-4xl mx-auto bg-white shadow-2xl border-0 overflow-hidden"
              onMouseEnter={() => setIsAutoPlaying(false)}
              onMouseLeave={() => setIsAutoPlaying(true)}
            >
              <CardContent className="p-8 md:p-12">
                <div className="flex flex-col md:flex-row items-center gap-8">
                  {/* Profile Image */}
                  <div className="flex-shrink-0">
                    <div className="relative">
                      <img
                        src={currentTestimonial.image}
                        alt={currentTestimonial.name}
                        className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover border-4 border-blue-200"
                      />
                      <div className="absolute -top-2 -right-2 bg-blue-600 text-white p-2 rounded-full">
                        <Quote className="w-4 h-4" />
                      </div>
                    </div>
                  </div>

                  {/* Testimonial Content */}
                  <div className="flex-1 text-center md:text-left">
                    {/* Rating */}
                    <div className="flex justify-center md:justify-start mb-4">
                      {[...Array(currentTestimonial.rating)].map((_, i) => (
                        <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                      ))}
                    </div>

                    {/* Testimonial Text */}
                    <blockquote className="text-lg md:text-xl text-gray-700 leading-relaxed mb-6 italic">
                      "{currentTestimonial.testimonial}"
                    </blockquote>

                    {/* Author Info */}
                    <div className="space-y-2">
                      <div className="font-bold text-xl text-gray-900">
                        {currentTestimonial.name}
                      </div>
                      <div className="text-gray-600">
                        {currentTestimonial.role}
                      </div>
                      <div className="flex flex-col sm:flex-row gap-2 justify-center md:justify-start">
                        <Badge variant="outline" className="text-blue-600 border-blue-200">
                          {currentTestimonial.course}
                        </Badge>
                        <Badge className="bg-green-100 text-green-700 border-green-200">
                          {currentTestimonial.achievement}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation Buttons */}
            <button
              onClick={goToPrev}
              className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 p-3 rounded-full transition-all duration-200 hover:scale-110"
            >
              <ChevronLeft className="w-6 h-6" />
            </button>
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 p-3 rounded-full transition-all duration-200 hover:scale-110"
            >
              <ChevronRight className="w-6 h-6" />
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="flex justify-center space-x-2 mb-12">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => goToSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-200 ${
                  index === currentIndex 
                    ? 'bg-blue-600 w-8' 
                    : 'bg-gray-300 hover:bg-gray-400'
                }`}
              />
            ))}
          </div>

          {/* All Testimonials Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {testimonials.map((testimonial, index) => (
              <Card 
                key={testimonial.id} 
                className={`hover:shadow-lg transition-all duration-300 cursor-pointer ${
                  index === currentIndex ? 'ring-2 ring-blue-400 shadow-lg' : ''
                }`}
                onClick={() => goToSlide(index)}
              >
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <img
                      src={testimonial.image}
                      alt={testimonial.name}
                      className="w-12 h-12 rounded-full object-cover mr-4"
                    />
                    <div>
                      <div className="font-semibold text-gray-900">{testimonial.name}</div>
                      <div className="text-sm text-gray-600">{testimonial.role}</div>
                    </div>
                  </div>
                  
                  <div className="flex mb-3">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  
                  <p className="text-gray-600 text-sm leading-relaxed line-clamp-3">
                    {testimonial.testimonial.substring(0, 120)}...
                  </p>
                  
                  <Badge variant="outline" className="mt-3 text-xs">
                    {testimonial.achievement}
                  </Badge>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Ready to Write Your Success Story?
              </h3>
              <p className="text-gray-600 mb-6">
                Join our community of successful students and experience the transformation yourself. 
                Your journey to academic excellence starts here.
              </p>
              <Button 
                size="lg"
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  contactSection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Start Your Journey Today
              </Button>
            </div>
          </div>
          </div>
        </div>
      </div>
    </section>
  );
}
