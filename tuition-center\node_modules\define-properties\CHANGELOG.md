# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.2.1](https://github.com/ljharb/define-properties/compare/v1.2.0...v1.2.1) - 2023-09-12

### Commits

- [Refactor] use `define-data-property` [`e7782a7`](https://github.com/ljharb/define-properties/commit/e7782a7480a62f8b6e141b49371e6de4df176c97)
- [actions] use reusable rebase action [`cd249c3`](https://github.com/ljharb/define-properties/commit/cd249c3920607bc8eeb7c0cd5b672b810983cac5)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`8205f97`](https://github.com/ljharb/define-properties/commit/8205f9734a4da8ee5b3b29798788567a09b330e8)

## [v1.2.0](https://github.com/ljharb/define-properties/compare/v1.1.4...v1.2.0) - 2023-02-10

### Commits

- [New] if the predicate is boolean `true`, it compares the existing value with `===` as the predicate [`d8dd6fc`](https://github.com/ljharb/define-properties/commit/d8dd6fca40d7c5878a4b643b91e66ae5a513a194)
- [meta] add `auto-changelog` [`7ebe2b0`](https://github.com/ljharb/define-properties/commit/7ebe2b0a0f90e62b842942cd45e86864fe75d9f6)
- [meta] use `npmignore` to autogenerate an npmignore file [`647478a`](https://github.com/ljharb/define-properties/commit/647478a8401fbf053fb633c0a3a7c982da6bad74)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`e620d70`](https://github.com/ljharb/define-properties/commit/e620d707d2e1118a38796f22a862200eb0a53fff)
- [Dev Deps] update `aud`, `tape` [`f1e5072`](https://github.com/ljharb/define-properties/commit/f1e507225c2551a99ed4fe40d3fe71b0f44acf88)
- [actions] update checkout action [`628b3af`](https://github.com/ljharb/define-properties/commit/628b3af5c74b8f0963296d811a8f6fa657baf964)

<!-- auto-changelog-above -->

1.1.4 / 2022-04-14
=================
 * [Refactor] use `has-property-descriptors`
 * [readme] add github actions/codecov badges
 * [Docs] fix header parsing; remove testling
 * [Deps] update `object-keys`
 * [meta] use `prepublishOnly` script for npm 7+
 * [meta] add `funding` field; create FUNDING.yml
 * [actions] add "Allow Edits" workflow; automatic rebasing / merge commit blocking
 * [actions] reuse common workflows
 * [actions] update codecov uploader
 * [actions] use `node/install` instead of `node/run`; use `codecov` action
 * [Tests] migrate tests to Github Actions
 * [Tests] run `nyc` on all tests; use `tape` runner
 * [Tests] use shared travis-ci config
 * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops
 * [Tests] remove `jscs`
 * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape`; add `aud`, `safe-publish-latest`

1.1.3 / 2018-08-14
=================
 * [Refactor] use a for loop instead of `foreach` to make for smaller bundle sizes
 * [Robustness] cache `Array.prototype.concat` and `Object.defineProperty`
 * [Deps] update `object-keys`
 * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `nsp`, `tape`, `jscs`; remove unused eccheck script + dep
 * [Tests] use pretest/posttest for linting/security
 * [Tests] fix npm upgrades on older nodes

1.1.2 / 2015-10-14
=================
 * [Docs] Switch from vb.teelaun.ch to versionbadg.es for the npm version badge SVG
 * [Deps] Update `object-keys`
 * [Dev Deps] update `jscs`, `tape`, `eslint`, `@ljharb/eslint-config`, `nsp`
 * [Tests] up to `io.js` `v3.3`, `node` `v4.2`

1.1.1 / 2015-07-21
=================
 * [Deps] Update `object-keys`
 * [Dev Deps] Update `tape`, `eslint`
 * [Tests] Test on `io.js` `v2.4`

1.1.0 / 2015-07-01
=================
 * [New] Add support for symbol-valued properties.
 * [Dev Deps] Update `nsp`, `eslint`
 * [Tests] Test up to `io.js` `v2.3`

1.0.3 / 2015-05-30
=================
 * Using a more reliable check for supported property descriptors.

1.0.2 / 2015-05-23
=================
 * Test up to `io.js` `v2.0`
 * Update `tape`, `jscs`, `nsp`, `eslint`, `object-keys`, `editorconfig-tools`, `covert`

1.0.1 / 2015-01-06
=================
 * Update `object-keys` to fix ES3 support

1.0.0 / 2015-01-04
=================
  * v1.0.0
