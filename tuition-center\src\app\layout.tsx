import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Excellence Academy Biratnagar - Best Tuition Center in Nepal | SEE, +2, Engineering & Medical Entrance Preparation",
  description: "Excellence Academy is Biratnagar's premier coaching institute offering SEE, +2 Science/Management, Engineering & Medical entrance preparation. 15+ years experience, 95% success rate, expert faculty. Enroll now!",
  keywords: [
    "Excellence Academy Biratnagar",
    "tuition center Biratnagar",
    "coaching institute Nepal",
    "SEE preparation Biratnagar",
    "+2 Science coaching",
    "+2 Management classes",
    "Engineering entrance preparation",
    "Medical entrance coaching",
    "IOE entrance preparation",
    "MBBS entrance coaching",
    "best tuition center Morang",
    "Biratnagar education",
    "Nepal coaching classes",
    "academic coaching Biratnagar"
  ].join(", "),
  authors: [{ name: "Excellence Academy Biratnagar" }],
  creator: "Excellence Academy Biratnagar",
  publisher: "Excellence Academy Biratnagar",
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://excellenceacademy.com.np',
    siteName: 'Excellence Academy Biratnagar',
    title: 'Excellence Academy Biratnagar - Best Tuition Center in Nepal',
    description: 'Premier coaching institute in Biratnagar offering SEE, +2, Engineering & Medical entrance preparation with 95% success rate and expert faculty.',
    images: [
      {
        url: 'https://images.unsplash.com/photo-1523050854058-8df90110c9d1?w=1200&h=630&fit=crop',
        width: 1200,
        height: 630,
        alt: 'Excellence Academy Biratnagar - Students in Modern Classroom',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Excellence Academy Biratnagar - Best Tuition Center in Nepal',
    description: 'Premier coaching institute offering SEE, +2, Engineering & Medical entrance preparation with 95% success rate.',
    images: ['https://images.unsplash.com/photo-1523050854058-8df90110c9d1?w=1200&h=630&fit=crop'],
    creator: '@excellenceacademy',
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
    yahoo: 'your-yahoo-verification-code',
  },
  alternates: {
    canonical: 'https://excellenceacademy.com.np',
  },
  category: 'Education',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <meta name="theme-color" content="#2563eb" />
        <link rel="icon" href="/favicon.ico" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <meta name="format-detection" content="telephone=yes" />
        <meta name="geo.region" content="NP-P1" />
        <meta name="geo.placename" content="Biratnagar" />
        <meta name="geo.position" content="26.4525;87.2718" />
        <meta name="ICBM" content="26.4525, 87.2718" />

        {/* Local Business Schema */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "EducationalOrganization",
              "name": "Excellence Academy Biratnagar",
              "alternateName": "Excellence Academy",
              "description": "Premier coaching institute in Biratnagar offering SEE, +2, Engineering & Medical entrance preparation",
              "url": "https://excellenceacademy.com.np",
              "telephone": "+977-21-123456",
              "email": "<EMAIL>",
              "address": {
                "@type": "PostalAddress",
                "streetAddress": "Main Road, Ward No. 10",
                "addressLocality": "Biratnagar",
                "addressRegion": "Morang",
                "addressCountry": "Nepal",
                "postalCode": "56613"
              },
              "geo": {
                "@type": "GeoCoordinates",
                "latitude": 26.4525,
                "longitude": 87.2718
              },
              "openingHours": [
                "Mo-Fr 06:00-20:00",
                "Sa-Su 06:00-18:00"
              ],
              "foundingDate": "2009",
              "founder": {
                "@type": "Person",
                "name": "Dr. Rajesh Kumar Sharma"
              },
              "numberOfEmployees": "50+",
              "areaServed": {
                "@type": "Place",
                "name": "Biratnagar, Morang, Province 1, Nepal"
              },
              "serviceType": [
                "SEE Preparation",
                "+2 Science Coaching",
                "+2 Management Coaching",
                "Engineering Entrance Preparation",
                "Medical Entrance Preparation",
                "Foundation Course"
              ],
              "aggregateRating": {
                "@type": "AggregateRating",
                "ratingValue": "4.8",
                "reviewCount": "500",
                "bestRating": "5",
                "worstRating": "1"
              },
              "image": "https://images.unsplash.com/photo-1523050854058-8df90110c9d1?w=1200&h=630&fit=crop",
              "logo": "https://excellenceacademy.com.np/logo.png",
              "sameAs": [
                "https://facebook.com/excellenceacademy",
                "https://instagram.com/excellenceacademy",
                "https://youtube.com/excellenceacademy"
              ]
            })
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
