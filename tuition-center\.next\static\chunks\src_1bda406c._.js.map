{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/HeroSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { GraduationCap, BookO<PERSON>, Users, Award } from \"lucide-react\";\n\nexport default function HeroSection() {\n  const scrollToContact = () => {\n    const contactSection = document.getElementById('contact');\n    contactSection?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white overflow-hidden\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"absolute inset-0 bg-gradient-to-br from-blue-800/20 to-indigo-800/20\"></div>\n        <div className=\"absolute inset-0\" style={{\n          backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n          backgroundSize: '60px 60px'\n        }}></div>\n      </div>\n      \n      {/* Floating Elements */}\n      <div className=\"absolute top-20 left-10 animate-bounce\">\n        <BookOpen className=\"w-8 h-8 text-blue-300 opacity-60\" />\n      </div>\n      <div className=\"absolute top-32 right-20 animate-pulse\">\n        <GraduationCap className=\"w-10 h-10 text-indigo-300 opacity-60\" />\n      </div>\n      <div className=\"absolute bottom-32 left-20 animate-bounce delay-1000\">\n        <Users className=\"w-6 h-6 text-blue-200 opacity-60\" />\n      </div>\n      <div className=\"absolute bottom-20 right-10 animate-pulse delay-500\">\n        <Award className=\"w-8 h-8 text-indigo-200 opacity-60\" />\n      </div>\n\n      <div className=\"container mx-auto px-4 text-center relative z-10\">\n        <div className=\"max-w-4xl mx-auto\">\n          {/* Main Heading */}\n          <h1 className=\"text-5xl md:text-7xl font-bold mb-6 leading-tight\">\n            Excel in Your\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500\">\n              Academic Journey\n            </span>\n          </h1>\n          \n          {/* Subheading */}\n          <p className=\"text-xl md:text-2xl mb-8 text-blue-100 max-w-3xl mx-auto leading-relaxed\">\n            Join Biratnagar's premier coaching institute and unlock your potential with expert guidance, \n            personalized attention, and proven results in SEE, +2, and entrance examinations.\n          </p>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mb-12 max-w-2xl mx-auto\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">15+</div>\n              <div className=\"text-sm text-blue-200\">Years Experience</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">2000+</div>\n              <div className=\"text-sm text-blue-200\">Students Taught</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">95%</div>\n              <div className=\"text-sm text-blue-200\">Success Rate</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-yellow-400\">50+</div>\n              <div className=\"text-sm text-blue-200\">Expert Teachers</div>\n            </div>\n          </div>\n\n          {/* CTA Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n            <Button \n              size=\"lg\" \n              className=\"bg-gradient-to-r from-yellow-500 to-orange-600 hover:from-yellow-600 hover:to-orange-700 text-white font-semibold px-8 py-3 text-lg shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200\"\n              onClick={scrollToContact}\n            >\n              Get Enrolled Now\n            </Button>\n            <Button \n              size=\"lg\" \n              variant=\"outline\" \n              className=\"border-2 border-white text-white hover:bg-white hover:text-blue-900 font-semibold px-8 py-3 text-lg transition-all duration-200\"\n              onClick={() => {\n                const aboutSection = document.getElementById('about');\n                aboutSection?.scrollIntoView({ behavior: 'smooth' });\n              }}\n            >\n              Learn More\n            </Button>\n          </div>\n\n          {/* Trust Indicators */}\n          <div className=\"mt-16 pt-8 border-t border-blue-700\">\n            <p className=\"text-blue-200 mb-4\">Trusted by students from:</p>\n            <div className=\"flex flex-wrap justify-center items-center gap-8 text-blue-300\">\n              <span className=\"text-sm\">Biratnagar Metropolitan</span>\n              <span className=\"text-sm\">•</span>\n              <span className=\"text-sm\">Morang District</span>\n              <span className=\"text-sm\">•</span>\n              <span className=\"text-sm\">Province 1</span>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-white rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-white rounded-full mt-2 animate-pulse\"></div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,kBAAkB;QACtB,MAAM,iBAAiB,SAAS,cAAc,CAAC;QAC/C,2BAAA,qCAAA,eAAgB,cAAc,CAAC;YAAE,UAAU;QAAS;IACtD;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAmB,OAAO;4BACvC,iBAAkB;4BAClB,gBAAgB;wBAClB;;;;;;;;;;;;0BAIF,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAEtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;0BAE3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAEnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAGnB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAG,WAAU;;gCAAoD;8CAEhE,6LAAC;oCAAK,WAAU;8CAAqF;;;;;;;;;;;;sCAMvG,6LAAC;4BAAE,WAAU;sCAA2E;;;;;;sCAMxF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;8CAEzC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqC;;;;;;sDACpD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;sCAK3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;oCACV,SAAS;8CACV;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;wCACP,MAAM,eAAe,SAAS,cAAc,CAAC;wCAC7C,yBAAA,mCAAA,aAAc,cAAc,CAAC;4CAAE,UAAU;wCAAS;oCACpD;8CACD;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOlC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB;KA/GwB", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAM6C;QAN7C,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD,GAN7C;IAOb,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAhBS", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/AboutSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Target, Heart, BookOpen, Users, Award, Clock } from \"lucide-react\";\n\nexport default function AboutSection() {\n  return (\n    <section id=\"about\" className=\"py-20 bg-gray-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              About Excellence Academy\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Nurturing Academic Excellence Since 2009\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Located in the heart of Biratnagar, we have been the trusted partner in thousands of students' \n              academic journeys, helping them achieve their dreams through quality education and personalized guidance.\n            </p>\n          </div>\n\n          {/* Main Content Grid */}\n          <div className=\"grid lg:grid-cols-2 gap-12 items-center mb-16\">\n            {/* Left Content */}\n            <div className=\"space-y-8\">\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 flex items-center\">\n                  <Target className=\"w-6 h-6 text-blue-600 mr-3\" />\n                  Our Mission\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  To provide world-class education that empowers students to excel in their academic pursuits \n                  and develop into confident, capable individuals ready to face future challenges. We believe \n                  every student has unique potential that deserves to be nurtured and developed.\n                </p>\n              </div>\n\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 flex items-center\">\n                  <Heart className=\"w-6 h-6 text-red-500 mr-3\" />\n                  Our Values\n                </h3>\n                <ul className=\"space-y-2 text-gray-600\">\n                  <li className=\"flex items-center\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                    Excellence in teaching and learning\n                  </li>\n                  <li className=\"flex items-center\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                    Individual attention to every student\n                  </li>\n                  <li className=\"flex items-center\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                    Integrity and ethical practices\n                  </li>\n                  <li className=\"flex items-center\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mr-3\"></div>\n                    Continuous innovation in education\n                  </li>\n                </ul>\n              </div>\n\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-900 mb-4 flex items-center\">\n                  <BookOpen className=\"w-6 h-6 text-green-600 mr-3\" />\n                  Teaching Approach\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Our proven methodology combines traditional teaching excellence with modern educational \n                  techniques. We focus on conceptual clarity, regular practice, and continuous assessment \n                  to ensure comprehensive learning and outstanding results.\n                </p>\n              </div>\n            </div>\n\n            {/* Right Content - Stats Cards */}\n            <div className=\"grid grid-cols-2 gap-6\">\n              <Card className=\"text-center p-6 hover:shadow-lg transition-shadow\">\n                <CardContent className=\"p-0\">\n                  <Users className=\"w-12 h-12 text-blue-600 mx-auto mb-4\" />\n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">2000+</div>\n                  <div className=\"text-gray-600\">Students Graduated</div>\n                </CardContent>\n              </Card>\n\n              <Card className=\"text-center p-6 hover:shadow-lg transition-shadow\">\n                <CardContent className=\"p-0\">\n                  <Award className=\"w-12 h-12 text-yellow-600 mx-auto mb-4\" />\n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">95%</div>\n                  <div className=\"text-gray-600\">Success Rate</div>\n                </CardContent>\n              </Card>\n\n              <Card className=\"text-center p-6 hover:shadow-lg transition-shadow\">\n                <CardContent className=\"p-0\">\n                  <Clock className=\"w-12 h-12 text-green-600 mx-auto mb-4\" />\n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">15+</div>\n                  <div className=\"text-gray-600\">Years Experience</div>\n                </CardContent>\n              </Card>\n\n              <Card className=\"text-center p-6 hover:shadow-lg transition-shadow\">\n                <CardContent className=\"p-0\">\n                  <BookOpen className=\"w-12 h-12 text-purple-600 mx-auto mb-4\" />\n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">50+</div>\n                  <div className=\"text-gray-600\">Expert Teachers</div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n\n          {/* Experience Timeline */}\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8 text-center\">Our Journey</h3>\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl font-bold text-blue-600\">2009</span>\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Foundation</h4>\n                <p className=\"text-gray-600 text-sm\">Started with a vision to provide quality education in Biratnagar</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl font-bold text-green-600\">2015</span>\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Expansion</h4>\n                <p className=\"text-gray-600 text-sm\">Expanded to include entrance exam preparation courses</p>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-2xl font-bold text-purple-600\">2024</span>\n                </div>\n                <h4 className=\"font-semibold text-gray-900 mb-2\">Excellence</h4>\n                <p className=\"text-gray-600 text-sm\">Recognized as the leading coaching institute in the region</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,6LAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAA+B;;;;;;;0DAGnD,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;kDAO/C,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAA8B;;;;;;;0DAGjD,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;4DAA8C;;;;;;;kEAG/D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;4DAA8C;;;;;;;kEAG/D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;4DAA8C;;;;;;;kEAG/D,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;;;;;;4DAA8C;;;;;;;;;;;;;;;;;;;kDAMnE,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC,iNAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDAAgC;;;;;;;0DAGtD,6LAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;0CASjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAInC,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAInC,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;kDAInC,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,6LAAC;oDAAI,WAAU;8DAAwC;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAClE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAmC;;;;;;;;;;;0DAErD,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAoC;;;;;;;;;;;0DAEtD,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;8DAAqC;;;;;;;;;;;0DAEvD,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrD;KA5IwB", "debugId": null}}, {"offset": {"line": 1252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/CoursesSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { \n  GraduationCap, \n  BookOpen, \n  Calculator, \n  Microscope, \n  Building, \n  Users, \n  Clock,\n  Star,\n  CheckCircle\n} from \"lucide-react\";\n\nconst courses = [\n  {\n    id: 1,\n    title: \"SEE Preparation\",\n    subtitle: \"Secondary Education Examination\",\n    icon: GraduationCap,\n    duration: \"1 Year\",\n    students: \"200+\",\n    rating: \"4.9\",\n    subjects: [\"Mathematics\", \"Science\", \"English\", \"Social Studies\", \"Nepali\", \"Optional Math\"],\n    features: [\n      \"Comprehensive syllabus coverage\",\n      \"Regular mock tests\",\n      \"Individual doubt clearing sessions\",\n      \"Study materials included\"\n    ],\n    color: \"blue\",\n    popular: true\n  },\n  {\n    id: 2,\n    title: \"+2 Science\",\n    subtitle: \"Physics, Chemistry, Biology & Mathematics\",\n    icon: Microscope,\n    duration: \"2 Years\",\n    students: \"150+\",\n    rating: \"4.8\",\n    subjects: [\"Physics\", \"Chemistry\", \"Biology\", \"Mathematics\", \"English\"],\n    features: [\n      \"Lab practical sessions\",\n      \"Concept-based learning\",\n      \"Board exam preparation\",\n      \"Career guidance included\"\n    ],\n    color: \"green\",\n    popular: false\n  },\n  {\n    id: 3,\n    title: \"+2 Management\",\n    subtitle: \"Business Studies & Economics\",\n    icon: Building,\n    duration: \"2 Years\",\n    students: \"120+\",\n    rating: \"4.7\",\n    subjects: [\"Accountancy\", \"Business Studies\", \"Economics\", \"Mathematics\", \"English\"],\n    features: [\n      \"Practical case studies\",\n      \"Industry exposure\",\n      \"Project-based learning\",\n      \"Internship opportunities\"\n    ],\n    color: \"purple\",\n    popular: false\n  },\n  {\n    id: 4,\n    title: \"Engineering Entrance\",\n    subtitle: \"IOE, Pulchowk, Thapathali\",\n    icon: Calculator,\n    duration: \"6 Months\",\n    students: \"80+\",\n    rating: \"4.9\",\n    subjects: [\"Physics\", \"Chemistry\", \"Mathematics\"],\n    features: [\n      \"Previous year questions\",\n      \"Time management techniques\",\n      \"Weekly assessments\",\n      \"Rank prediction tests\"\n    ],\n    color: \"orange\",\n    popular: true\n  },\n  {\n    id: 5,\n    title: \"Medical Entrance\",\n    subtitle: \"MBBS, BDS, Nursing\",\n    icon: BookOpen,\n    duration: \"6 Months\",\n    students: \"60+\",\n    rating: \"4.8\",\n    subjects: [\"Biology\", \"Chemistry\", \"Physics\"],\n    features: [\n      \"NEET pattern preparation\",\n      \"Medical terminology focus\",\n      \"Diagram practice sessions\",\n      \"Interview preparation\"\n    ],\n    color: \"red\",\n    popular: false\n  },\n  {\n    id: 6,\n    title: \"Foundation Course\",\n    subtitle: \"Class 8, 9 & 10\",\n    icon: Users,\n    duration: \"Flexible\",\n    students: \"300+\",\n    rating: \"4.6\",\n    subjects: [\"All Core Subjects\", \"Skill Development\", \"Personality Development\"],\n    features: [\n      \"Strong foundation building\",\n      \"Interactive learning methods\",\n      \"Parent-teacher meetings\",\n      \"Progress tracking\"\n    ],\n    color: \"indigo\",\n    popular: false\n  }\n];\n\nconst getColorClasses = (color: string) => {\n  const colors = {\n    blue: \"border-blue-200 bg-blue-50 text-blue-600\",\n    green: \"border-green-200 bg-green-50 text-green-600\",\n    purple: \"border-purple-200 bg-purple-50 text-purple-600\",\n    orange: \"border-orange-200 bg-orange-50 text-orange-600\",\n    red: \"border-red-200 bg-red-50 text-red-600\",\n    indigo: \"border-indigo-200 bg-indigo-50 text-indigo-600\"\n  };\n  return colors[color as keyof typeof colors] || colors.blue;\n};\n\nexport default function CoursesSection() {\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Our Courses\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Comprehensive Academic Programs\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Choose from our wide range of courses designed to help you excel in your academic journey. \n              Each program is carefully crafted with expert guidance and proven methodologies.\n            </p>\n          </div>\n\n          {/* Courses Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-8\">\n            {courses.map((course) => {\n              const IconComponent = course.icon;\n              return (\n                <Card \n                  key={course.id} \n                  className={`relative hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 ${\n                    course.popular ? 'ring-2 ring-yellow-400' : ''\n                  }`}\n                >\n                  {course.popular && (\n                    <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                      <Badge className=\"bg-yellow-500 text-white\">\n                        <Star className=\"w-3 h-3 mr-1\" />\n                        Popular\n                      </Badge>\n                    </div>\n                  )}\n                  \n                  <CardHeader className=\"pb-4\">\n                    <div className={`w-16 h-16 rounded-full border-2 ${getColorClasses(course.color)} flex items-center justify-center mb-4`}>\n                      <IconComponent className=\"w-8 h-8\" />\n                    </div>\n                    \n                    <CardTitle className=\"text-xl font-bold text-gray-900 mb-2\">\n                      {course.title}\n                    </CardTitle>\n                    <p className=\"text-gray-600 text-sm mb-4\">{course.subtitle}</p>\n                    \n                    <div className=\"flex items-center justify-between text-sm text-gray-500\">\n                      <div className=\"flex items-center\">\n                        <Clock className=\"w-4 h-4 mr-1\" />\n                        {course.duration}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Users className=\"w-4 h-4 mr-1\" />\n                        {course.students}\n                      </div>\n                      <div className=\"flex items-center\">\n                        <Star className=\"w-4 h-4 mr-1 text-yellow-500\" />\n                        {course.rating}\n                      </div>\n                    </div>\n                  </CardHeader>\n                  \n                  <CardContent className=\"pt-0\">\n                    <div className=\"mb-4\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Subjects Covered:</h4>\n                      <div className=\"flex flex-wrap gap-1\">\n                        {course.subjects.slice(0, 3).map((subject, index) => (\n                          <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                            {subject}\n                          </Badge>\n                        ))}\n                        {course.subjects.length > 3 && (\n                          <Badge variant=\"outline\" className=\"text-xs\">\n                            +{course.subjects.length - 3} more\n                          </Badge>\n                        )}\n                      </div>\n                    </div>\n                    \n                    <div className=\"mb-6\">\n                      <h4 className=\"font-semibold text-gray-900 mb-2\">Key Features:</h4>\n                      <ul className=\"space-y-1\">\n                        {course.features.slice(0, 3).map((feature, index) => (\n                          <li key={index} className=\"flex items-center text-sm text-gray-600\">\n                            <CheckCircle className=\"w-3 h-3 text-green-500 mr-2 flex-shrink-0\" />\n                            {feature}\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                    \n                    <Button \n                      className=\"w-full\" \n                      variant={course.popular ? \"default\" : \"outline\"}\n                      onClick={() => {\n                        const contactSection = document.getElementById('contact');\n                        contactSection?.scrollIntoView({ behavior: 'smooth' });\n                      }}\n                    >\n                      Enroll Now\n                    </Button>\n                  </CardContent>\n                </Card>\n              );\n            })}\n          </div>\n\n          {/* Additional Info */}\n          <div className=\"mt-16 text-center\">\n            <div className=\"bg-gray-50 rounded-2xl p-8\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Can't find the right course?\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                We also offer customized courses and one-on-one tutoring sessions. \n                Contact us to discuss your specific academic needs.\n              </p>\n              <Button \n                size=\"lg\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Contact Us for Custom Courses\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;;AAiBA,MAAM,UAAU;IACd;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,2NAAA,CAAA,gBAAa;QACnB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAe;YAAW;YAAW;YAAkB;YAAU;SAAgB;QAC5F,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,iNAAA,CAAA,aAAU;QAChB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAW;YAAa;YAAW;YAAe;SAAU;QACvE,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,6MAAA,CAAA,WAAQ;QACd,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAe;YAAoB;YAAa;YAAe;SAAU;QACpF,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,iNAAA,CAAA,aAAU;QAChB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAW;YAAa;SAAc;QACjD,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,iNAAA,CAAA,WAAQ;QACd,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAW;YAAa;SAAU;QAC7C,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,MAAM,uMAAA,CAAA,QAAK;QACX,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;YAAC;YAAqB;YAAqB;SAA0B;QAC/E,UAAU;YACR;YACA;YACA;YACA;SACD;QACD,OAAO;QACP,SAAS;IACX;CACD;AAED,MAAM,kBAAkB,CAAC;IACvB,MAAM,SAAS;QACb,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,QAAQ;IACV;IACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;AAC5D;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,6LAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC;4BACZ,MAAM,gBAAgB,OAAO,IAAI;4BACjC,qBACE,6LAAC,mIAAA,CAAA,OAAI;gCAEH,WAAW,AAAC,uFAEX,OADC,OAAO,OAAO,GAAG,2BAA2B;;oCAG7C,OAAO,OAAO,kBACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;kDAMvC,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,6LAAC;gDAAI,WAAW,AAAC,mCAAgE,OAA9B,gBAAgB,OAAO,KAAK,GAAE;0DAC/E,cAAA,6LAAC;oDAAc,WAAU;;;;;;;;;;;0DAG3B,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAClB,OAAO,KAAK;;;;;;0DAEf,6LAAC;gDAAE,WAAU;0DAA8B,OAAO,QAAQ;;;;;;0DAE1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,OAAO,QAAQ;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;4DAChB,OAAO,QAAQ;;;;;;;kEAElB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DACf,OAAO,MAAM;;;;;;;;;;;;;;;;;;;kDAKpB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAI,WAAU;;4DACZ,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC,oIAAA,CAAA,QAAK;oEAAa,SAAQ;oEAAU,WAAU;8EAC5C;mEADS;;;;;4DAIb,OAAO,QAAQ,CAAC,MAAM,GAAG,mBACxB,6LAAC,oIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;oEAAU;oEACzC,OAAO,QAAQ,CAAC,MAAM,GAAG;oEAAE;;;;;;;;;;;;;;;;;;;0DAMrC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,6LAAC;wDAAG,WAAU;kEACX,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,SAAS,sBACzC,6LAAC;gEAAe,WAAU;;kFACxB,6LAAC,8NAAA,CAAA,cAAW;wEAAC,WAAU;;;;;;oEACtB;;+DAFM;;;;;;;;;;;;;;;;0DAQf,6LAAC,qIAAA,CAAA,SAAM;gDACL,WAAU;gDACV,SAAS,OAAO,OAAO,GAAG,YAAY;gDACtC,SAAS;oDACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;oDAC/C,2BAAA,qCAAA,eAAgB,cAAc,CAAC;wDAAE,UAAU;oDAAS;gDACtD;0DACD;;;;;;;;;;;;;+BA5EE,OAAO,EAAE;;;;;wBAkFpB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;wCACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;wCAC/C,2BAAA,qCAAA,eAAgB,cAAc,CAAC;4CAAE,UAAU;wCAAS;oCACtD;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAvIwB", "debugId": null}}, {"offset": {"line": 1798, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/WhyChooseUsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  Users, \n  Award, \n  Clock, \n  BookOpen, \n  Target, \n  TrendingUp,\n  CheckCircle,\n  Star,\n  Brain,\n  Shield,\n  Zap,\n  Heart\n} from \"lucide-react\";\n\nconst features = [\n  {\n    icon: Users,\n    title: \"Expert Faculty\",\n    description: \"Our team consists of highly qualified teachers with 10+ years of experience in their respective subjects.\",\n    stats: \"50+ Expert Teachers\",\n    color: \"blue\"\n  },\n  {\n    icon: Target,\n    title: \"Small Batch Sizes\",\n    description: \"We maintain small class sizes (15-20 students) to ensure personalized attention and better learning outcomes.\",\n    stats: \"Max 20 Students/Batch\",\n    color: \"green\"\n  },\n  {\n    icon: Clock,\n    title: \"Regular Assessments\",\n    description: \"Weekly tests, monthly evaluations, and mock exams to track progress and identify areas for improvement.\",\n    stats: \"Weekly Tests\",\n    color: \"purple\"\n  },\n  {\n    icon: TrendingUp,\n    title: \"Proven Results\",\n    description: \"95% of our students achieve their target scores with many securing top ranks in their respective examinations.\",\n    stats: \"95% Success Rate\",\n    color: \"orange\"\n  },\n  {\n    icon: BookOpen,\n    title: \"Comprehensive Study Material\",\n    description: \"Well-researched notes, practice papers, and reference materials designed by our expert faculty.\",\n    stats: \"Complete Study Package\",\n    color: \"red\"\n  },\n  {\n    icon: Brain,\n    title: \"Modern Teaching Methods\",\n    description: \"Interactive learning, visual aids, and technology-enhanced teaching for better concept understanding.\",\n    stats: \"Interactive Learning\",\n    color: \"indigo\"\n  },\n  {\n    icon: Shield,\n    title: \"Safe Learning Environment\",\n    description: \"Clean, well-ventilated classrooms with proper safety measures and a conducive learning atmosphere.\",\n    stats: \"Safe & Secure\",\n    color: \"teal\"\n  },\n  {\n    icon: Heart,\n    title: \"Individual Care\",\n    description: \"Personal mentoring, career guidance, and emotional support to help students overcome challenges.\",\n    stats: \"Personal Mentoring\",\n    color: \"pink\"\n  }\n];\n\nconst achievements = [\n  {\n    number: \"2000+\",\n    label: \"Students Graduated\",\n    icon: Users\n  },\n  {\n    number: \"95%\",\n    label: \"Success Rate\",\n    icon: Award\n  },\n  {\n    number: \"15+\",\n    label: \"Years Experience\",\n    icon: Clock\n  },\n  {\n    number: \"50+\",\n    label: \"Expert Teachers\",\n    icon: BookOpen\n  }\n];\n\nconst getColorClasses = (color: string) => {\n  const colors = {\n    blue: \"bg-blue-100 text-blue-600\",\n    green: \"bg-green-100 text-green-600\",\n    purple: \"bg-purple-100 text-purple-600\",\n    orange: \"bg-orange-100 text-orange-600\",\n    red: \"bg-red-100 text-red-600\",\n    indigo: \"bg-indigo-100 text-indigo-600\",\n    teal: \"bg-teal-100 text-teal-600\",\n    pink: \"bg-pink-100 text-pink-600\"\n  };\n  return colors[color as keyof typeof colors] || colors.blue;\n};\n\nexport default function WhyChooseUsSection() {\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Why Choose Us\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              What Makes Us Different\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              We don't just teach subjects; we build futures. Our unique approach combines academic excellence \n              with personal development to ensure holistic growth of every student.\n            </p>\n          </div>\n\n          {/* Features Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16\">\n            {features.map((feature, index) => {\n              const IconComponent = feature.icon;\n              return (\n                <Card key={index} className=\"hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 border-0 bg-white/80 backdrop-blur-sm\">\n                  <CardContent className=\"p-6 text-center\">\n                    <div className={`w-16 h-16 rounded-full ${getColorClasses(feature.color)} flex items-center justify-center mx-auto mb-4`}>\n                      <IconComponent className=\"w-8 h-8\" />\n                    </div>\n                    <h3 className=\"text-lg font-bold text-gray-900 mb-2\">\n                      {feature.title}\n                    </h3>\n                    <p className=\"text-gray-600 text-sm mb-3 leading-relaxed\">\n                      {feature.description}\n                    </p>\n                    <Badge variant=\"outline\" className=\"text-xs font-medium\">\n                      {feature.stats}\n                    </Badge>\n                  </CardContent>\n                </Card>\n              );\n            })}\n          </div>\n\n          {/* Achievements Section */}\n          <div className=\"bg-white rounded-2xl p-8 shadow-lg mb-16\">\n            <h3 className=\"text-2xl font-bold text-gray-900 text-center mb-8\">\n              Our Achievements Speak for Themselves\n            </h3>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n              {achievements.map((achievement, index) => {\n                const IconComponent = achievement.icon;\n                return (\n                  <div key={index} className=\"text-center\">\n                    <div className=\"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <IconComponent className=\"w-8 h-8 text-blue-600\" />\n                    </div>\n                    <div className=\"text-3xl font-bold text-gray-900 mb-2\">\n                      {achievement.number}\n                    </div>\n                    <div className=\"text-gray-600 text-sm\">\n                      {achievement.label}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Testimonial Highlight */}\n          <div className=\"bg-gradient-to-r from-blue-600 to-indigo-700 rounded-2xl p-8 text-white text-center\">\n            <div className=\"flex justify-center mb-4\">\n              {[...Array(5)].map((_, i) => (\n                <Star key={i} className=\"w-6 h-6 text-yellow-400 fill-current\" />\n              ))}\n            </div>\n            <blockquote className=\"text-xl md:text-2xl font-medium mb-4 italic\">\n              \"Excellence Academy transformed my academic journey. The personalized attention and expert guidance \n              helped me secure admission to my dream engineering college.\"\n            </blockquote>\n            <div className=\"text-blue-200\">\n              <div className=\"font-semibold\">Rajesh Sharma</div>\n              <div className=\"text-sm\">IOE Pulchowk Graduate, 2023</div>\n            </div>\n          </div>\n\n          {/* Call to Action */}\n          <div className=\"text-center mt-16\">\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n              Ready to Start Your Success Journey?\n            </h3>\n            <p className=\"text-gray-600 mb-6 max-w-2xl mx-auto\">\n              Join thousands of successful students who have achieved their academic goals with our guidance. \n              Your success story starts here.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button \n                className=\"bg-blue-600 hover:bg-blue-700 text-white font-semibold px-8 py-3 rounded-lg transition-colors\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Get Enrolled Today\n              </button>\n              <button \n                className=\"border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white font-semibold px-8 py-3 rounded-lg transition-colors\"\n                onClick={() => {\n                  const aboutSection = document.getElementById('about');\n                  aboutSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Learn More About Us\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAmBA,MAAM,WAAW;IACf;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,qNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,iNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;QACb,OAAO;QACP,OAAO;IACT;CACD;AAED,MAAM,eAAe;IACnB;QACE,QAAQ;QACR,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,QAAQ;QACR,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,QAAQ;QACR,OAAO;QACP,MAAM,uMAAA,CAAA,QAAK;IACb;IACA;QACE,QAAQ;QACR,OAAO;QACP,MAAM,iNAAA,CAAA,WAAQ;IAChB;CACD;AAED,MAAM,kBAAkB,CAAC;IACvB,MAAM,SAAS;QACb,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,QAAQ;QACR,MAAM;QACN,MAAM;IACR;IACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;AAC5D;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS;4BACtB,MAAM,gBAAgB,QAAQ,IAAI;4BAClC,qBACE,6LAAC,mIAAA,CAAA,OAAI;gCAAa,WAAU;0CAC1B,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAW,AAAC,0BAAwD,OAA/B,gBAAgB,QAAQ,KAAK,GAAE;sDACvE,cAAA,6LAAC;gDAAc,WAAU;;;;;;;;;;;sDAE3B,6LAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,6LAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAEtB,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAChC,QAAQ,KAAK;;;;;;;;;;;;+BAZT;;;;;wBAiBf;;;;;;kCAIF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,aAAa;oCAC9B,MAAM,gBAAgB,YAAY,IAAI;oCACtC,qBACE,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAc,WAAU;;;;;;;;;;;0DAE3B,6LAAC;gDAAI,WAAU;0DACZ,YAAY,MAAM;;;;;;0DAErB,6LAAC;gDAAI,WAAU;0DACZ,YAAY,KAAK;;;;;;;uCARZ;;;;;gCAYd;;;;;;;;;;;;kCAKJ,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC,qMAAA,CAAA,OAAI;wCAAS,WAAU;uCAAb;;;;;;;;;;0CAGf,6LAAC;gCAAW,WAAU;0CAA8C;;;;;;0CAIpE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAI,WAAU;kDAAU;;;;;;;;;;;;;;;;;;kCAK7B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,WAAU;wCACV,SAAS;4CACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;4CAC/C,2BAAA,qCAAA,eAAgB,cAAc,CAAC;gDAAE,UAAU;4CAAS;wCACtD;kDACD;;;;;;kDAGD,6LAAC;wCACC,WAAU;wCACV,SAAS;4CACP,MAAM,eAAe,SAAS,cAAc,CAAC;4CAC7C,yBAAA,mCAAA,aAAc,cAAc,CAAC;gDAAE,UAAU;4CAAS;wCACpD;kDACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KAxHwB", "debugId": null}}, {"offset": {"line": 2234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/GallerySection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { \n  Users, \n  Award, \n  BookOpen, \n  Camera,\n  X,\n  ChevronLeft,\n  ChevronRight\n} from \"lucide-react\";\n\n// Mock gallery data - in a real app, these would be actual images\nconst galleryItems = [\n  {\n    id: 1,\n    title: \"SEE Batch 2023 - Classroom Session\",\n    category: \"classroom\",\n    description: \"Interactive mathematics class with our expert faculty\",\n    image: \"https://images.unsplash.com/photo-1509062522246-3755977927d7?w=600&h=400&fit=crop\",\n    alt: \"Students in classroom\"\n  },\n  {\n    id: 2,\n    title: \"Science Laboratory Session\",\n    category: \"activities\",\n    description: \"Hands-on chemistry experiments for +2 Science students\",\n    image: \"https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=600&h=400&fit=crop\",\n    alt: \"Science laboratory\"\n  },\n  {\n    id: 3,\n    title: \"Excellence Award 2023\",\n    category: \"certificates\",\n    description: \"Recognition for outstanding academic performance\",\n    image: \"https://images.unsplash.com/photo-1567427017947-545c5f8d16ad?w=600&h=400&fit=crop\",\n    alt: \"Award ceremony\"\n  },\n  {\n    id: 4,\n    title: \"Group Study Session\",\n    category: \"activities\",\n    description: \"Collaborative learning environment for entrance preparation\",\n    image: \"https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=600&h=400&fit=crop\",\n    alt: \"Group study\"\n  },\n  {\n    id: 5,\n    title: \"Faculty Training Workshop\",\n    category: \"activities\",\n    description: \"Continuous professional development of our teaching staff\",\n    image: \"https://images.unsplash.com/photo-1524178232363-1fb2b075b655?w=600&h=400&fit=crop\",\n    alt: \"Faculty workshop\"\n  },\n  {\n    id: 6,\n    title: \"Student Achievement Certificate\",\n    category: \"certificates\",\n    description: \"Top performer in Engineering Entrance Examination\",\n    image: \"https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=600&h=400&fit=crop\",\n    alt: \"Achievement certificate\"\n  },\n  {\n    id: 7,\n    title: \"Modern Classroom Facility\",\n    category: \"classroom\",\n    description: \"Well-equipped classrooms with modern teaching aids\",\n    image: \"https://images.unsplash.com/photo-1497486751825-1233686d5d80?w=600&h=400&fit=crop\",\n    alt: \"Modern classroom\"\n  },\n  {\n    id: 8,\n    title: \"Annual Function 2023\",\n    category: \"activities\",\n    description: \"Celebrating academic achievements and cultural activities\",\n    image: \"https://images.unsplash.com/photo-1511632765486-a01980e01a18?w=600&h=400&fit=crop\",\n    alt: \"Annual function\"\n  },\n  {\n    id: 9,\n    title: \"Best Institute Award\",\n    category: \"certificates\",\n    description: \"Recognized as the leading coaching institute in Biratnagar\",\n    image: \"https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=600&h=400&fit=crop\",\n    alt: \"Institute award\"\n  }\n];\n\nconst categories = [\n  { id: \"all\", label: \"All\", icon: Camera },\n  { id: \"classroom\", label: \"Classrooms\", icon: BookOpen },\n  { id: \"activities\", label: \"Activities\", icon: Users },\n  { id: \"certificates\", label: \"Achievements\", icon: Award }\n];\n\nexport default function GallerySection() {\n  const [activeCategory, setActiveCategory] = useState(\"all\");\n  const [selectedImage, setSelectedImage] = useState<typeof galleryItems[0] | null>(null);\n\n  const filteredItems = activeCategory === \"all\" \n    ? galleryItems \n    : galleryItems.filter(item => item.category === activeCategory);\n\n  const openModal = (item: typeof galleryItems[0]) => {\n    setSelectedImage(item);\n  };\n\n  const closeModal = () => {\n    setSelectedImage(null);\n  };\n\n  const navigateImage = (direction: 'prev' | 'next') => {\n    if (!selectedImage) return;\n    \n    const currentIndex = filteredItems.findIndex(item => item.id === selectedImage.id);\n    let newIndex;\n    \n    if (direction === 'prev') {\n      newIndex = currentIndex > 0 ? currentIndex - 1 : filteredItems.length - 1;\n    } else {\n      newIndex = currentIndex < filteredItems.length - 1 ? currentIndex + 1 : 0;\n    }\n    \n    setSelectedImage(filteredItems[newIndex]);\n  };\n\n  return (\n    <section className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Gallery\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Glimpses of Excellence\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Take a look at our vibrant learning environment, student activities, and achievements \n              that showcase the excellence we strive for every day.\n            </p>\n          </div>\n\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap justify-center gap-4 mb-12\">\n            {categories.map((category) => {\n              const IconComponent = category.icon;\n              return (\n                <Button\n                  key={category.id}\n                  variant={activeCategory === category.id ? \"default\" : \"outline\"}\n                  onClick={() => setActiveCategory(category.id)}\n                  className=\"flex items-center gap-2\"\n                >\n                  <IconComponent className=\"w-4 h-4\" />\n                  {category.label}\n                </Button>\n              );\n            })}\n          </div>\n\n          {/* Gallery Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {filteredItems.map((item) => (\n              <Card \n                key={item.id} \n                className=\"overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 cursor-pointer group\"\n                onClick={() => openModal(item)}\n              >\n                <div className=\"relative overflow-hidden\">\n                  <img\n                    src={item.image}\n                    alt={item.alt}\n                    className=\"w-full h-64 object-cover transition-transform duration-300 group-hover:scale-110\"\n                  />\n                  <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center\">\n                    <Camera className=\"w-8 h-8 text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n                  </div>\n                </div>\n                <CardContent className=\"p-4\">\n                  <h3 className=\"font-bold text-gray-900 mb-2\">{item.title}</h3>\n                  <p className=\"text-gray-600 text-sm\">{item.description}</p>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {/* Modal */}\n          {selectedImage && (\n            <div className=\"fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4\">\n              <div className=\"relative max-w-4xl max-h-full\">\n                {/* Close Button */}\n                <button\n                  onClick={closeModal}\n                  className=\"absolute top-4 right-4 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all\"\n                >\n                  <X className=\"w-6 h-6\" />\n                </button>\n\n                {/* Navigation Buttons */}\n                <button\n                  onClick={() => navigateImage('prev')}\n                  className=\"absolute left-4 top-1/2 transform -translate-y-1/2 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all\"\n                >\n                  <ChevronLeft className=\"w-6 h-6\" />\n                </button>\n                <button\n                  onClick={() => navigateImage('next')}\n                  className=\"absolute right-4 top-1/2 transform -translate-y-1/2 z-10 bg-white bg-opacity-20 hover:bg-opacity-30 text-white p-2 rounded-full transition-all\"\n                >\n                  <ChevronRight className=\"w-6 h-6\" />\n                </button>\n\n                {/* Image */}\n                <img\n                  src={selectedImage.image}\n                  alt={selectedImage.alt}\n                  className=\"max-w-full max-h-[80vh] object-contain rounded-lg\"\n                />\n\n                {/* Image Info */}\n                <div className=\"absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6 text-white\">\n                  <h3 className=\"text-xl font-bold mb-2\">{selectedImage.title}</h3>\n                  <p className=\"text-gray-300\">{selectedImage.description}</p>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Call to Action */}\n          <div className=\"text-center mt-16\">\n            <div className=\"bg-gray-50 rounded-2xl p-8\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Want to Be Part of Our Success Story?\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Join our community of achievers and create your own success story. \n                Experience the difference quality education makes.\n              </p>\n              <Button \n                size=\"lg\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Start Your Journey Today\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAgBA,kEAAkE;AAClE,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;IACA;QACE,IAAI;QACJ,OAAO;QACP,UAAU;QACV,aAAa;QACb,OAAO;QACP,KAAK;IACP;CACD;AAED,MAAM,aAAa;IACjB;QAAE,IAAI;QAAO,OAAO;QAAO,MAAM,yMAAA,CAAA,SAAM;IAAC;IACxC;QAAE,IAAI;QAAa,OAAO;QAAc,MAAM,iNAAA,CAAA,WAAQ;IAAC;IACvD;QAAE,IAAI;QAAc,OAAO;QAAc,MAAM,uMAAA,CAAA,QAAK;IAAC;IACrD;QAAE,IAAI;QAAgB,OAAO;QAAgB,MAAM,uMAAA,CAAA,QAAK;IAAC;CAC1D;AAEc,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAElF,MAAM,gBAAgB,mBAAmB,QACrC,eACA,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;IAElD,MAAM,YAAY,CAAC;QACjB,iBAAiB;IACnB;IAEA,MAAM,aAAa;QACjB,iBAAiB;IACnB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,CAAC,eAAe;QAEpB,MAAM,eAAe,cAAc,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,cAAc,EAAE;QACjF,IAAI;QAEJ,IAAI,cAAc,QAAQ;YACxB,WAAW,eAAe,IAAI,eAAe,IAAI,cAAc,MAAM,GAAG;QAC1E,OAAO;YACL,WAAW,eAAe,cAAc,MAAM,GAAG,IAAI,eAAe,IAAI;QAC1E;QAEA,iBAAiB,aAAa,CAAC,SAAS;IAC1C;IAEA,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,gBAAgB,SAAS,IAAI;4BACnC,qBACE,6LAAC,qIAAA,CAAA,SAAM;gCAEL,SAAS,mBAAmB,SAAS,EAAE,GAAG,YAAY;gCACtD,SAAS,IAAM,kBAAkB,SAAS,EAAE;gCAC5C,WAAU;;kDAEV,6LAAC;wCAAc,WAAU;;;;;;oCACxB,SAAS,KAAK;;+BANV,SAAS,EAAE;;;;;wBAStB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,qBAClB,6LAAC,mIAAA,CAAA,OAAI;gCAEH,WAAU;gCACV,SAAS,IAAM,UAAU;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,KAAK,KAAK,KAAK;gDACf,KAAK,KAAK,GAAG;gDACb,WAAU;;;;;;0DAEZ,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAGtB,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAG,WAAU;0DAAgC,KAAK,KAAK;;;;;;0DACxD,6LAAC;gDAAE,WAAU;0DAAyB,KAAK,WAAW;;;;;;;;;;;;;+BAhBnD,KAAK,EAAE;;;;;;;;;;oBAuBjB,+BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;8CAIf,6LAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,6LAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAI1B,6LAAC;oCACC,KAAK,cAAc,KAAK;oCACxB,KAAK,cAAc,GAAG;oCACtB,WAAU;;;;;;8CAIZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B,cAAc,KAAK;;;;;;sDAC3D,6LAAC;4CAAE,WAAU;sDAAiB,cAAc,WAAW;;;;;;;;;;;;;;;;;;;;;;;kCAO/D,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;wCACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;wCAC/C,2BAAA,qCAAA,eAAgB,cAAc,CAAC;4CAAE,UAAU;wCAAS;oCACtD;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAhKwB;KAAA", "debugId": null}}, {"offset": {"line": 2691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/TestimonialsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { Card, CardContent } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Star, Quote, ChevronLeft, ChevronRight, User } from \"lucide-react\";\n\nconst testimonials = [\n  {\n    id: 1,\n    name: \"<PERSON><PERSON>\",\n    role: \"SEE Graduate 2023\",\n    course: \"SEE Preparation\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"Excellence Academy completely transformed my approach to studying. The teachers here don't just teach subjects; they build confidence. I scored 3.95 GPA in SEE, which seemed impossible before joining here. The individual attention and regular tests helped me identify my weak areas and improve them systematically.\",\n    achievement: \"3.95 GPA in SEE\"\n  },\n  {\n    id: 2,\n    name: \"<PERSON><PERSON>\",\n    role: \"Engineering Student\",\n    course: \"Engineering Entrance\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"The entrance preparation course at Excellence Academy is simply outstanding. The faculty's deep understanding of exam patterns and their teaching methodology helped me crack IOE entrance exam. The mock tests were exactly like the real exam, which boosted my confidence tremendously.\",\n    achievement: \"IOE Pulchowk Admission\"\n  },\n  {\n    id: 3,\n    name: \"Sunita Rai\",\n    role: \"Parent\",\n    course: \"+2 Science\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"As a parent, I was initially worried about my daughter's performance in +2 Science. But Excellence Academy's supportive environment and excellent teaching staff helped her excel. The regular parent-teacher meetings kept me informed about her progress. She's now pursuing MBBS!\",\n    achievement: \"Daughter got MBBS admission\"\n  },\n  {\n    id: 4,\n    name: \"Amit Kumar\",\n    role: \"+2 Management Graduate\",\n    course: \"+2 Management\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"The practical approach to teaching business studies and economics at Excellence Academy is remarkable. The teachers use real-world examples that made complex concepts easy to understand. I secured first division and got admission to my preferred college for BBS.\",\n    achievement: \"First Division in +2\"\n  },\n  {\n    id: 5,\n    name: \"Anita Gurung\",\n    role: \"Medical Student\",\n    course: \"Medical Entrance\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-**********-94ddf0286df2?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"The medical entrance preparation at Excellence Academy is top-notch. The biology faculty's expertise and the comprehensive study materials provided gave me the edge I needed. The regular practice sessions and doubt-clearing classes were incredibly helpful in my MBBS entrance preparation.\",\n    achievement: \"MBBS Admission Secured\"\n  },\n  {\n    id: 6,\n    name: \"Krishna Bahadur\",\n    role: \"Parent\",\n    course: \"Foundation Course\",\n    rating: 5,\n    image: \"https://images.unsplash.com/photo-**********-0b93528c311a?w=100&h=100&fit=crop&crop=face\",\n    testimonial: \"My son was struggling with mathematics in class 9. Excellence Academy's foundation course not only improved his grades but also built his confidence. The teachers are patient and understanding. Now he's one of the top students in his class. Highly recommended!\",\n    achievement: \"Son improved from C to A grade\"\n  }\n];\n\nexport default function TestimonialsSection() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  // Auto-play functionality\n  useEffect(() => {\n    if (!isAutoPlaying) return;\n    \n    const interval = setInterval(() => {\n      setCurrentIndex((prevIndex) => \n        prevIndex === testimonials.length - 1 ? 0 : prevIndex + 1\n      );\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying]);\n\n  const goToNext = () => {\n    setCurrentIndex(currentIndex === testimonials.length - 1 ? 0 : currentIndex + 1);\n  };\n\n  const goToPrev = () => {\n    setCurrentIndex(currentIndex === 0 ? testimonials.length - 1 : currentIndex - 1);\n  };\n\n  const goToSlide = (index: number) => {\n    setCurrentIndex(index);\n  };\n\n  const currentTestimonial = testimonials[currentIndex];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Testimonials\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              What Our Students Say\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Don't just take our word for it. Hear from our successful students and parents \n              who have experienced the Excellence Academy difference firsthand.\n            </p>\n          </div>\n\n          {/* Main Testimonial Card */}\n          <div className=\"relative mb-12\">\n            <Card \n              className=\"max-w-4xl mx-auto bg-white shadow-2xl border-0 overflow-hidden\"\n              onMouseEnter={() => setIsAutoPlaying(false)}\n              onMouseLeave={() => setIsAutoPlaying(true)}\n            >\n              <CardContent className=\"p-8 md:p-12\">\n                <div className=\"flex flex-col md:flex-row items-center gap-8\">\n                  {/* Profile Image */}\n                  <div className=\"flex-shrink-0\">\n                    <div className=\"relative\">\n                      <img\n                        src={currentTestimonial.image}\n                        alt={currentTestimonial.name}\n                        className=\"w-24 h-24 md:w-32 md:h-32 rounded-full object-cover border-4 border-blue-200\"\n                      />\n                      <div className=\"absolute -top-2 -right-2 bg-blue-600 text-white p-2 rounded-full\">\n                        <Quote className=\"w-4 h-4\" />\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Testimonial Content */}\n                  <div className=\"flex-1 text-center md:text-left\">\n                    {/* Rating */}\n                    <div className=\"flex justify-center md:justify-start mb-4\">\n                      {[...Array(currentTestimonial.rating)].map((_, i) => (\n                        <Star key={i} className=\"w-5 h-5 text-yellow-400 fill-current\" />\n                      ))}\n                    </div>\n\n                    {/* Testimonial Text */}\n                    <blockquote className=\"text-lg md:text-xl text-gray-700 leading-relaxed mb-6 italic\">\n                      \"{currentTestimonial.testimonial}\"\n                    </blockquote>\n\n                    {/* Author Info */}\n                    <div className=\"space-y-2\">\n                      <div className=\"font-bold text-xl text-gray-900\">\n                        {currentTestimonial.name}\n                      </div>\n                      <div className=\"text-gray-600\">\n                        {currentTestimonial.role}\n                      </div>\n                      <div className=\"flex flex-col sm:flex-row gap-2 justify-center md:justify-start\">\n                        <Badge variant=\"outline\" className=\"text-blue-600 border-blue-200\">\n                          {currentTestimonial.course}\n                        </Badge>\n                        <Badge className=\"bg-green-100 text-green-700 border-green-200\">\n                          {currentTestimonial.achievement}\n                        </Badge>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Navigation Buttons */}\n            <button\n              onClick={goToPrev}\n              className=\"absolute left-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 p-3 rounded-full transition-all duration-200 hover:scale-110\"\n            >\n              <ChevronLeft className=\"w-6 h-6\" />\n            </button>\n            <button\n              onClick={goToNext}\n              className=\"absolute right-4 top-1/2 transform -translate-y-1/2 bg-white shadow-lg hover:shadow-xl text-gray-600 hover:text-blue-600 p-3 rounded-full transition-all duration-200 hover:scale-110\"\n            >\n              <ChevronRight className=\"w-6 h-6\" />\n            </button>\n          </div>\n\n          {/* Dots Indicator */}\n          <div className=\"flex justify-center space-x-2 mb-12\">\n            {testimonials.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => goToSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-200 ${\n                  index === currentIndex \n                    ? 'bg-blue-600 w-8' \n                    : 'bg-gray-300 hover:bg-gray-400'\n                }`}\n              />\n            ))}\n          </div>\n\n          {/* All Testimonials Grid */}\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {testimonials.map((testimonial, index) => (\n              <Card \n                key={testimonial.id} \n                className={`hover:shadow-lg transition-all duration-300 cursor-pointer ${\n                  index === currentIndex ? 'ring-2 ring-blue-400 shadow-lg' : ''\n                }`}\n                onClick={() => goToSlide(index)}\n              >\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center mb-4\">\n                    <img\n                      src={testimonial.image}\n                      alt={testimonial.name}\n                      className=\"w-12 h-12 rounded-full object-cover mr-4\"\n                    />\n                    <div>\n                      <div className=\"font-semibold text-gray-900\">{testimonial.name}</div>\n                      <div className=\"text-sm text-gray-600\">{testimonial.role}</div>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex mb-3\">\n                    {[...Array(testimonial.rating)].map((_, i) => (\n                      <Star key={i} className=\"w-4 h-4 text-yellow-400 fill-current\" />\n                    ))}\n                  </div>\n                  \n                  <p className=\"text-gray-600 text-sm leading-relaxed line-clamp-3\">\n                    {testimonial.testimonial.substring(0, 120)}...\n                  </p>\n                  \n                  <Badge variant=\"outline\" className=\"mt-3 text-xs\">\n                    {testimonial.achievement}\n                  </Badge>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {/* Call to Action */}\n          <div className=\"text-center mt-16\">\n            <div className=\"bg-white rounded-2xl p-8 shadow-lg\">\n              <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">\n                Ready to Write Your Success Story?\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                Join our community of successful students and experience the transformation yourself. \n                Your journey to academic excellence starts here.\n              </p>\n              <Button \n                size=\"lg\"\n                onClick={() => {\n                  const contactSection = document.getElementById('contact');\n                  contactSection?.scrollIntoView({ behavior: 'smooth' });\n                }}\n              >\n                Start Your Journey Today\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;AANA;;;;;;AAQA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,aAAa;QACb,aAAa;IACf;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0BAA0B;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,CAAC,eAAe;YAEpB,MAAM,WAAW;0DAAY;oBAC3B;kEAAgB,CAAC,YACf,cAAc,aAAa,MAAM,GAAG,IAAI,IAAI,YAAY;;gBAE5D;yDAAG;YAEH;iDAAO,IAAM,cAAc;;QAC7B;wCAAG;QAAC;KAAc;IAElB,MAAM,WAAW;QACf,gBAAgB,iBAAiB,aAAa,MAAM,GAAG,IAAI,IAAI,eAAe;IAChF;IAEA,MAAM,WAAW;QACf,gBAAgB,iBAAiB,IAAI,aAAa,MAAM,GAAG,IAAI,eAAe;IAChF;IAEA,MAAM,YAAY,CAAC;QACjB,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,YAAY,CAAC,aAAa;IAErD,qBACE,6LAAC;QAAQ,WAAU;kBACjB,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAOzE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,mIAAA,CAAA,OAAI;gCACH,WAAU;gCACV,cAAc,IAAM,iBAAiB;gCACrC,cAAc,IAAM,iBAAiB;0CAErC,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DACC,KAAK,mBAAmB,KAAK;4DAC7B,KAAK,mBAAmB,IAAI;4DAC5B,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0DAMvB,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;kEACZ;+DAAI,MAAM,mBAAmB,MAAM;yDAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBAC7C,6LAAC,qMAAA,CAAA,OAAI;gEAAS,WAAU;+DAAb;;;;;;;;;;kEAKf,6LAAC;wDAAW,WAAU;;4DAA+D;4DACjF,mBAAmB,WAAW;4DAAC;;;;;;;kEAInC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ,mBAAmB,IAAI;;;;;;0EAE1B,6LAAC;gEAAI,WAAU;0EACZ,mBAAmB,IAAI;;;;;;0EAE1B,6LAAC;gEAAI,WAAU;;kFACb,6LAAC,oIAAA,CAAA,QAAK;wEAAC,SAAQ;wEAAU,WAAU;kFAChC,mBAAmB,MAAM;;;;;;kFAE5B,6LAAC,oIAAA,CAAA,QAAK;wEAAC,WAAU;kFACd,mBAAmB,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAU7C,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,uNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAEzB,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK5B,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,GAAG,sBACpB,6LAAC;gCAEC,SAAS,IAAM,UAAU;gCACzB,WAAW,AAAC,oDAIX,OAHC,UAAU,eACN,oBACA;+BALD;;;;;;;;;;kCAYX,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,6LAAC,mIAAA,CAAA,OAAI;gCAEH,WAAW,AAAC,8DAEX,OADC,UAAU,eAAe,mCAAmC;gCAE9D,SAAS,IAAM,UAAU;0CAEzB,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,KAAK,YAAY,KAAK;oDACtB,KAAK,YAAY,IAAI;oDACrB,WAAU;;;;;;8DAEZ,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAA+B,YAAY,IAAI;;;;;;sEAC9D,6LAAC;4DAAI,WAAU;sEAAyB,YAAY,IAAI;;;;;;;;;;;;;;;;;;sDAI5D,6LAAC;4CAAI,WAAU;sDACZ;mDAAI,MAAM,YAAY,MAAM;6CAAE,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtC,6LAAC,qMAAA,CAAA,OAAI;oDAAS,WAAU;mDAAb;;;;;;;;;;sDAIf,6LAAC;4CAAE,WAAU;;gDACV,YAAY,WAAW,CAAC,SAAS,CAAC,GAAG;gDAAK;;;;;;;sDAG7C,6LAAC,oIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAChC,YAAY,WAAW;;;;;;;;;;;;+BA9BvB,YAAY,EAAE;;;;;;;;;;kCAsCzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAIlC,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;wCACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;wCAC/C,2BAAA,qCAAA,eAAgB,cAAc,CAAC;4CAAE,UAAU;wCAAS;oCACtD;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GA5MwB;KAAA", "debugId": null}}, {"offset": {"line": 3230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,KAA4D;QAA5D,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC,GAA5D;IACb,qBACE,6LAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3263, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,KAAyD;QAAzD,EAAE,SAAS,EAAE,GAAG,OAAyC,GAAzD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS", "debugId": null}}, {"offset": {"line": 3295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,oKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 3330, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/sections/ContactSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { \n  Phone, \n  Mail, \n  MapPin, \n  Clock, \n  Send,\n  CheckCircle,\n  User,\n  MessageSquare,\n  BookOpen\n} from \"lucide-react\";\n\nconst courses = [\n  \"SEE Preparation\",\n  \"+2 Science\",\n  \"+2 Management\", \n  \"Engineering Entrance\",\n  \"Medical Entrance\",\n  \"Foundation Course (Class 8-10)\",\n  \"Other\"\n];\n\nconst contactInfo = [\n  {\n    icon: Phone,\n    title: \"Phone Numbers\",\n    details: [\"+977-21-123456\", \"+977-**********\"],\n    color: \"green\"\n  },\n  {\n    icon: Mail,\n    title: \"Email Address\",\n    details: [\"<EMAIL>\", \"<EMAIL>\"],\n    color: \"blue\"\n  },\n  {\n    icon: MapPin,\n    title: \"Address\",\n    details: [\"Main Road, Biratnagar-10\", \"Morang, Province 1, Nepal\"],\n    color: \"red\"\n  },\n  {\n    icon: Clock,\n    title: \"Office Hours\",\n    details: [\"Mon-Fri: 6:00 AM - 8:00 PM\", \"Sat-Sun: 6:00 AM - 6:00 PM\"],\n    color: \"purple\"\n  }\n];\n\nexport default function ContactSection() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    phone: \"\",\n    email: \"\",\n    course: \"\",\n    message: \"\"\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    setIsSubmitting(false);\n    setIsSubmitted(true);\n    \n    // Reset form after 3 seconds\n    setTimeout(() => {\n      setIsSubmitted(false);\n      setFormData({\n        name: \"\",\n        phone: \"\",\n        email: \"\",\n        course: \"\",\n        message: \"\"\n      });\n    }, 3000);\n  };\n\n  const getColorClasses = (color: string) => {\n    const colors = {\n      green: \"bg-green-100 text-green-600\",\n      blue: \"bg-blue-100 text-blue-600\",\n      red: \"bg-red-100 text-red-600\",\n      purple: \"bg-purple-100 text-purple-600\"\n    };\n    return colors[color as keyof typeof colors] || colors.blue;\n  };\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-white\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-6xl mx-auto\">\n          {/* Section Header */}\n          <div className=\"text-center mb-16\">\n            <Badge variant=\"secondary\" className=\"mb-4 text-blue-600 bg-blue-100\">\n              Contact Us\n            </Badge>\n            <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n              Get in Touch\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n              Ready to start your academic journey with us? We're here to help you choose the right course \n              and answer all your questions. Contact us today!\n            </p>\n          </div>\n\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <Card className=\"shadow-lg border-0\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl font-bold text-gray-900 flex items-center\">\n                  <MessageSquare className=\"w-6 h-6 mr-3 text-blue-600\" />\n                  Send us a Message\n                </CardTitle>\n              </CardHeader>\n              <CardContent>\n                {isSubmitted ? (\n                  <div className=\"text-center py-8\">\n                    <div className=\"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                      <CheckCircle className=\"w-8 h-8 text-green-600\" />\n                    </div>\n                    <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Message Sent Successfully!</h3>\n                    <p className=\"text-gray-600\">\n                      Thank you for your interest. We'll get back to you within 24 hours.\n                    </p>\n                  </div>\n                ) : (\n                  <form onSubmit={handleSubmit} className=\"space-y-6\">\n                    <div className=\"grid md:grid-cols-2 gap-4\">\n                      <div>\n                        <Label htmlFor=\"name\" className=\"flex items-center mb-2\">\n                          <User className=\"w-4 h-4 mr-2\" />\n                          Full Name *\n                        </Label>\n                        <Input\n                          id=\"name\"\n                          name=\"name\"\n                          type=\"text\"\n                          required\n                          value={formData.name}\n                          onChange={handleInputChange}\n                          placeholder=\"Enter your full name\"\n                          className=\"h-12\"\n                        />\n                      </div>\n                      <div>\n                        <Label htmlFor=\"phone\" className=\"flex items-center mb-2\">\n                          <Phone className=\"w-4 h-4 mr-2\" />\n                          Phone Number *\n                        </Label>\n                        <Input\n                          id=\"phone\"\n                          name=\"phone\"\n                          type=\"tel\"\n                          required\n                          value={formData.phone}\n                          onChange={handleInputChange}\n                          placeholder=\"Enter your phone number\"\n                          className=\"h-12\"\n                        />\n                      </div>\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"email\" className=\"flex items-center mb-2\">\n                        <Mail className=\"w-4 h-4 mr-2\" />\n                        Email Address\n                      </Label>\n                      <Input\n                        id=\"email\"\n                        name=\"email\"\n                        type=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        placeholder=\"Enter your email address\"\n                        className=\"h-12\"\n                      />\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"course\" className=\"flex items-center mb-2\">\n                        <BookOpen className=\"w-4 h-4 mr-2\" />\n                        Course of Interest *\n                      </Label>\n                      <select\n                        id=\"course\"\n                        name=\"course\"\n                        required\n                        value={formData.course}\n                        onChange={handleInputChange}\n                        className=\"w-full h-12 px-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                      >\n                        <option value=\"\">Select a course</option>\n                        {courses.map((course, index) => (\n                          <option key={index} value={course}>\n                            {course}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n\n                    <div>\n                      <Label htmlFor=\"message\" className=\"flex items-center mb-2\">\n                        <MessageSquare className=\"w-4 h-4 mr-2\" />\n                        Message\n                      </Label>\n                      <Textarea\n                        id=\"message\"\n                        name=\"message\"\n                        value={formData.message}\n                        onChange={handleInputChange}\n                        placeholder=\"Tell us about your academic goals, questions, or any specific requirements...\"\n                        className=\"min-h-[120px] resize-none\"\n                      />\n                    </div>\n\n                    <Button \n                      type=\"submit\" \n                      className=\"w-full h-12 text-lg font-semibold\"\n                      disabled={isSubmitting}\n                    >\n                      {isSubmitting ? (\n                        <>\n                          <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                          Sending...\n                        </>\n                      ) : (\n                        <>\n                          <Send className=\"w-4 h-4 mr-2\" />\n                          Send Message\n                        </>\n                      )}\n                    </Button>\n                  </form>\n                )}\n              </CardContent>\n            </Card>\n\n            {/* Contact Information */}\n            <div className=\"space-y-6\">\n              <Card className=\"shadow-lg border-0\">\n                <CardHeader>\n                  <CardTitle className=\"text-2xl font-bold text-gray-900\">\n                    Contact Information\n                  </CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-6\">\n                  {contactInfo.map((info, index) => {\n                    const IconComponent = info.icon;\n                    return (\n                      <div key={index} className=\"flex items-start space-x-4\">\n                        <div className={`w-12 h-12 rounded-full ${getColorClasses(info.color)} flex items-center justify-center flex-shrink-0`}>\n                          <IconComponent className=\"w-5 h-5\" />\n                        </div>\n                        <div>\n                          <h3 className=\"font-semibold text-gray-900 mb-1\">{info.title}</h3>\n                          {info.details.map((detail, detailIndex) => (\n                            <p key={detailIndex} className=\"text-gray-600 text-sm\">\n                              {detail}\n                            </p>\n                          ))}\n                        </div>\n                      </div>\n                    );\n                  })}\n                </CardContent>\n              </Card>\n\n              {/* Quick Info */}\n              <Card className=\"shadow-lg border-0 bg-gradient-to-br from-blue-50 to-indigo-50\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                    Why Contact Us?\n                  </h3>\n                  <ul className=\"space-y-3\">\n                    <li className=\"flex items-center text-gray-700\">\n                      <CheckCircle className=\"w-4 h-4 text-green-500 mr-3 flex-shrink-0\" />\n                      Free consultation and course guidance\n                    </li>\n                    <li className=\"flex items-center text-gray-700\">\n                      <CheckCircle className=\"w-4 h-4 text-green-500 mr-3 flex-shrink-0\" />\n                      Flexible batch timings available\n                    </li>\n                    <li className=\"flex items-center text-gray-700\">\n                      <CheckCircle className=\"w-4 h-4 text-green-500 mr-3 flex-shrink-0\" />\n                      Special discounts for early enrollment\n                    </li>\n                    <li className=\"flex items-center text-gray-700\">\n                      <CheckCircle className=\"w-4 h-4 text-green-500 mr-3 flex-shrink-0\" />\n                      Demo classes available\n                    </li>\n                  </ul>\n                </CardContent>\n              </Card>\n\n              {/* Emergency Contact */}\n              <Card className=\"shadow-lg border-0 bg-gradient-to-br from-red-50 to-pink-50\">\n                <CardContent className=\"p-6\">\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-4\">\n                    Emergency Contact\n                  </h3>\n                  <p className=\"text-gray-600 mb-3\">\n                    For urgent inquiries or admission-related emergencies:\n                  </p>\n                  <div className=\"flex items-center text-red-600 font-semibold\">\n                    <Phone className=\"w-4 h-4 mr-2\" />\n                    +977-********** (24/7)\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;;AAqBA,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,cAAc;IAClB;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAAkB;SAAkB;QAC9C,OAAO;IACT;IACA;QACE,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,SAAS;YAAC;YAA8B;SAAkC;QAC1E,OAAO;IACT;IACA;QACE,MAAM,6MAAA,CAAA,SAAM;QACZ,OAAO;QACP,SAAS;YAAC;YAA4B;SAA4B;QAClE,OAAO;IACT;IACA;QACE,MAAM,uMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAA8B;SAA6B;QACrE,OAAO;IACT;CACD;AAEc,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,eAAe;QAEf,6BAA6B;QAC7B,WAAW;YACT,eAAe;YACf,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,QAAQ;gBACR,SAAS;YACX;QACF,GAAG;IACL;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS;YACb,OAAO;YACP,MAAM;YACN,KAAK;YACL,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,MAA6B,IAAI,OAAO,IAAI;IAC5D;IAEA,qBACE,6LAAC;QAAQ,IAAG;QAAU,WAAU;kBAC9B,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAAiC;;;;;;0CAGtE,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAGlE,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;;;;;;kDAI5D,6LAAC,mIAAA,CAAA,cAAW;kDACT,4BACC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,8NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAEzB,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;iEAK/B,6LAAC;4CAAK,UAAU;4CAAc,WAAU;;8DACtC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAO,WAAU;;sFAC9B,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGnC,6LAAC,oIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,QAAQ;oEACR,OAAO,SAAS,IAAI;oEACpB,UAAU;oEACV,aAAY;oEACZ,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC,oIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAQ,WAAU;;sFAC/B,6LAAC,uMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;8EAGpC,6LAAC,oIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAK;oEACL,MAAK;oEACL,QAAQ;oEACR,OAAO,SAAS,KAAK;oEACrB,UAAU;oEACV,aAAY;oEACZ,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;;8EAC/B,6LAAC,qMAAA,CAAA,OAAI;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGnC,6LAAC,oIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAS,WAAU;;8EAChC,6LAAC,iNAAA,CAAA,WAAQ;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAGvC,6LAAC;4DACC,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,MAAM;4DACtB,UAAU;4DACV,WAAU;;8EAEV,6LAAC;oEAAO,OAAM;8EAAG;;;;;;gEAChB,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;wEAAmB,OAAO;kFACxB;uEADU;;;;;;;;;;;;;;;;;8DAOnB,6LAAC;;sEACC,6LAAC,oIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;8EACjC,6LAAC,2NAAA,CAAA,gBAAa;oEAAC,WAAU;;;;;;gEAAiB;;;;;;;sEAG5C,6LAAC,uIAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAId,6LAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,WAAU;oDACV,UAAU;8DAET,6BACC;;0EACE,6LAAC;gEAAI,WAAU;;;;;;4DAAuE;;qFAIxF;;0EACE,6LAAC,qMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;;;;;;;0CAW/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAmC;;;;;;;;;;;0DAI1D,6LAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACpB,YAAY,GAAG,CAAC,CAAC,MAAM;oDACtB,MAAM,gBAAgB,KAAK,IAAI;oDAC/B,qBACE,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAI,WAAW,AAAC,0BAAqD,OAA5B,gBAAgB,KAAK,KAAK,GAAE;0EACpE,cAAA,6LAAC;oEAAc,WAAU;;;;;;;;;;;0EAE3B,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAoC,KAAK,KAAK;;;;;;oEAC3D,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,6LAAC;4EAAoB,WAAU;sFAC5B;2EADK;;;;;;;;;;;;uDAPJ;;;;;gDAcd;;;;;;;;;;;;kDAKJ,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAGrD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;sEAGvE,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC,8NAAA,CAAA,cAAW;oEAAC,WAAU;;;;;;gEAA8C;;;;;;;;;;;;;;;;;;;;;;;;kDAQ7E,6LAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DAGrD,6LAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAGlC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,uMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWtD;GAxRwB;KAAA", "debugId": null}}, {"offset": {"line": 4114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/training/tuition-center/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Separator({\n  className,\n  orientation = \"horizontal\",\n  decorative = true,\n  ...props\n}: React.ComponentProps<typeof SeparatorPrimitive.Root>) {\n  return (\n    <SeparatorPrimitive.Root\n      data-slot=\"separator\"\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,UAAU,KAKoC;QALpC,EACjB,SAAS,EACT,cAAc,YAAY,EAC1B,aAAa,IAAI,EACjB,GAAG,OACkD,GALpC;IAMjB,qBACE,6LAAC,wKAAA,CAAA,OAAuB;QACtB,aAAU;QACV,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kKACA;QAED,GAAG,KAAK;;;;;;AAGf;KAlBS", "debugId": null}}]}