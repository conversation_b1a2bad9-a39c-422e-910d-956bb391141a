"use client";

import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>H<PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  GraduationCap, 
  BookOpen, 
  Calculator, 
  Microscope, 
  Building, 
  Users, 
  Clock,
  Star,
  CheckCircle
} from "lucide-react";

const courses = [
  {
    id: 1,
    title: "SEE Preparation",
    subtitle: "Secondary Education Examination",
    icon: GraduationCap,
    image: "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=400&h=250&fit=crop",
    duration: "1 Year",
    students: "200+",
    rating: "4.9",
    subjects: ["Mathematics", "Science", "English", "Social Studies", "Nepali", "Optional Math"],
    features: [
      "Comprehensive syllabus coverage",
      "Regular mock tests",
      "Individual doubt clearing sessions",
      "Study materials included"
    ],
    color: "blue",
    popular: true
  },
  {
    id: 2,
    title: "+2 Science",
    subtitle: "Physics, Chemistry, Biology & Mathematics",
    icon: Microscope,
    image: "https://images.unsplash.com/photo-1532094349884-543bc11b234d?w=400&h=250&fit=crop",
    duration: "2 Years",
    students: "150+",
    rating: "4.8",
    subjects: ["Physics", "Chemistry", "Biology", "Mathematics", "English"],
    features: [
      "Lab practical sessions",
      "Concept-based learning",
      "Board exam preparation",
      "Career guidance included"
    ],
    color: "green",
    popular: false
  },
  {
    id: 3,
    title: "+2 Management",
    subtitle: "Business Studies & Economics",
    icon: Building,
    image: "https://images.unsplash.com/photo-*************-c3d57bc86b40?w=400&h=250&fit=crop",
    duration: "2 Years",
    students: "120+",
    rating: "4.7",
    subjects: ["Accountancy", "Business Studies", "Economics", "Mathematics", "English"],
    features: [
      "Practical case studies",
      "Industry exposure",
      "Project-based learning",
      "Internship opportunities"
    ],
    color: "purple",
    popular: false
  },
  {
    id: 4,
    title: "Engineering Entrance",
    subtitle: "IOE, Pulchowk, Thapathali",
    icon: Calculator,
    image: "https://images.unsplash.com/photo-*************-a6a2a5aee158?w=400&h=250&fit=crop",
    duration: "6 Months",
    students: "80+",
    rating: "4.9",
    subjects: ["Physics", "Chemistry", "Mathematics"],
    features: [
      "Previous year questions",
      "Time management techniques",
      "Weekly assessments",
      "Rank prediction tests"
    ],
    color: "orange",
    popular: true
  },
  {
    id: 5,
    title: "Medical Entrance",
    subtitle: "MBBS, BDS, Nursing",
    icon: BookOpen,
    image: "https://images.unsplash.com/photo-**********-5c350d0d3c56?w=400&h=250&fit=crop",
    duration: "6 Months",
    students: "60+",
    rating: "4.8",
    subjects: ["Biology", "Chemistry", "Physics"],
    features: [
      "NEET pattern preparation",
      "Medical terminology focus",
      "Diagram practice sessions",
      "Interview preparation"
    ],
    color: "red",
    popular: false
  },
  {
    id: 6,
    title: "Foundation Course",
    subtitle: "Class 8, 9 & 10",
    icon: Users,
    image: "https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=400&h=250&fit=crop",
    duration: "Flexible",
    students: "300+",
    rating: "4.6",
    subjects: ["All Core Subjects", "Skill Development", "Personality Development"],
    features: [
      "Strong foundation building",
      "Interactive learning methods",
      "Parent-teacher meetings",
      "Progress tracking"
    ],
    color: "indigo",
    popular: false
  }
];



export default function CoursesSection() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <Badge variant="secondary" className="mb-4 text-blue-600 bg-blue-100">
              Our Courses
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Comprehensive Academic Programs
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Choose from our wide range of courses designed to help you excel in your academic journey. 
              Each program is carefully crafted with expert guidance and proven methodologies.
            </p>
          </div>

          {/* Courses Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {courses.map((course) => {
              const IconComponent = course.icon;
              return (
                <Card
                  key={course.id}
                  className="group relative overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border-0"
                >
                  
                  {/* Course Image */}
                  <div className="relative h-48 overflow-hidden">
                    <Image
                      src={course.image}
                      alt={course.title}
                      width={400}
                      height={192}
                      className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
                    <div className={`absolute top-4 left-4 w-12 h-12 rounded-full border-2 border-white bg-white/90 backdrop-blur-sm flex items-center justify-center`}>
                      <IconComponent className="w-6 h-6 text-gray-700" />
                    </div>
                    {course.popular && (
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-yellow-500 text-white">
                          <Star className="w-3 h-3 mr-1" />
                          Popular
                        </Badge>
                      </div>
                    )}
                  </div>

                  <CardHeader className="pb-4">
                    
                    <CardTitle className="text-xl font-bold text-gray-900 mb-2">
                      {course.title}
                    </CardTitle>
                    <p className="text-gray-600 text-sm mb-4">{course.subtitle}</p>
                    
                    <div className="flex items-center justify-between text-sm text-gray-500">
                      <div className="flex items-center">
                        <Clock className="w-4 h-4 mr-1" />
                        {course.duration}
                      </div>
                      <div className="flex items-center">
                        <Users className="w-4 h-4 mr-1" />
                        {course.students}
                      </div>
                      <div className="flex items-center">
                        <Star className="w-4 h-4 mr-1 text-yellow-500" />
                        {course.rating}
                      </div>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    <div className="mb-4">
                      <h4 className="font-semibold text-gray-900 mb-2">Subjects Covered:</h4>
                      <div className="flex flex-wrap gap-1">
                        {course.subjects.slice(0, 3).map((subject, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {subject}
                          </Badge>
                        ))}
                        {course.subjects.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{course.subjects.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    <div className="mb-6">
                      <h4 className="font-semibold text-gray-900 mb-2">Key Features:</h4>
                      <ul className="space-y-1">
                        {course.features.slice(0, 3).map((feature, index) => (
                          <li key={index} className="flex items-center text-sm text-gray-600">
                            <CheckCircle className="w-3 h-3 text-green-500 mr-2 flex-shrink-0" />
                            {feature}
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <Button 
                      className="w-full" 
                      variant={course.popular ? "default" : "outline"}
                      onClick={() => {
                        const contactSection = document.getElementById('contact');
                        contactSection?.scrollIntoView({ behavior: 'smooth' });
                      }}
                    >
                      Enroll Now
                    </Button>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* Additional Info */}
          <div className="mt-16 text-center">
            <div className="bg-gray-50 rounded-2xl p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Can&apos;t find the right course?
              </h3>
              <p className="text-gray-600 mb-6">
                We also offer customized courses and one-on-one tutoring sessions. 
                Contact us to discuss your specific academic needs.
              </p>
              <Button 
                size="lg"
                onClick={() => {
                  const contactSection = document.getElementById('contact');
                  contactSection?.scrollIntoView({ behavior: 'smooth' });
                }}
              >
                Contact Us for Custom Courses
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
